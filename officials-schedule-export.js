'use strict';
const argv 		= require('optimist').argv,
	  xlsx 		= require('xlsx'),
	  pg      	= require('pg'),
	  path    	= require('path'),
      { parseParameterObject } = require('./sw-utils/export-helper'),


	  $EVENT_ID 	= parseInt(argv.event, 10),
	  $CONN_STR 	= argv.connection,
      $FILE_NAME    = argv.filename,
      $INCLUDE_MATCH_CODES = argv.includeMatchCodes === 'true';

if(!$EVENT_ID) {
	process.stderr.write('Invalid Event Identifier')
	process.exit(1)
}

if(!$CONN_STR) {
	process.stderr.write('Invalid connection string')
	process.exit(1)
}

parseParameterObject($CONN_STR)
.then(function connectToDB (connection) {
	var client = new pg.Client(connection);
    client.connect();
    return client;
})
.then(function getEventDaysDates (pgClient) {
	return pgClient.query(
		`SELECT 
		    "days"."day", (
		        SELECT ARRAY_TO_JSON(ARRAY_AGG(TO_CHAR(secs.secs_start, 'HH12:MI AM')))
		        FROM (
		            SELECT DISTINCT date_trunc('minutes', m.secs_start) "secs_start"
		            FROM "matches" m
		            WHERE m.event_id = $1
		                AND TO_CHAR(m.secs_start, 'YYYY-MM-DD') = days.day
		             ORDER BY date_trunc('minutes', m.secs_start)
		        ) "secs"
		    ) "match_hours"
		 FROM (
		 SELECT TO_CHAR(dd, 'YYYY-MM-DD') "day", dd
		 FROM (
		    	SELECT GENERATE_SERIES(e.date_start, e.date_end, '1 day'::INTERVAL) dd
		    	FROM "event" e
		    	WHERE e.event_id = $1
		 ) "event_days"
		 ) "days"
		 GROUP BY days.day, days.dd
		 ORDER BY days.dd`,
		[$EVENT_ID]
	).then(function (result) {
		if(result.rowCount === 0) {
			throw 'Event has no Dates'
		}
		return {
			pgClient 	: pgClient,
			eventDates 	: result.rows || []
		}
	})
})
.then(function generateSheets (data) {
	return Promise.all(
		data.eventDates.map(function (eventDateData) {
			return __exportEventDay(data.pgClient, eventDateData)
		})
	).then(function appendSheetsToWorkbook(sheets) {
		var workbook = {
			SheetNames 	: [],
			Sheets 		: {}
		}, missingCount = 0;
		sheets.forEach(function (sheet) {
			if(sheet) {
				workbook.SheetNames.push(sheet.name);
				workbook.Sheets[sheet.name] = sheet.data;
			} else {
				missingCount++;
			}
		})
		return (missingCount === sheets.length)
					?null
					:workbook
	})
})
.then(function writeFile (workbook) {
	if(!workbook)
		return null;
	var filepath = path.resolve(__dirname, '..', 'export', `${$FILE_NAME}-${new Date().getTime()}.xlsx`)
	xlsx.writeFile(workbook, filepath,  { font: { name: "Verdana" }, bookSST: true })
	return filepath
})
.then(function (filepath) {
	if(filepath) {
		process.stdout.write(filepath)
	}
	process.exit(0)
}, function (error) {
	process.stderr.write(error)
	process.exit(1)
})

function __exportEventDay (pgClient, eventDateData) {
	if(!(eventDateData.match_hours && eventDateData.match_hours.length))
		return null;
	return pgClient.query(
		__COURTS_MATCHES_SQL,
		[$EVENT_ID, eventDateData.day]
	).then(function (result) {
		return result.rows || []
	})
	.then(function (courtsList) {
		if(courtsList.length > 0) {
			return __generateReversedSheet(courtsList, eventDateData)
		} else {
			return null
		}
	})
}

function __generateReversedSheet (courts, eventDateData) {
	let sheet 			= {},
		sheetLine 		= 0,
		sheetColumn 	= 0, 
		columnsLength 	= courts.length,
		linesLength 	= eventDateData.match_hours.length,
		eventHours 		= eventDateData.match_hours,
		matchIndex 		= 0,
		currentMatch,
		currentCourt,
		currentTime, 
		cellName,
		courtMatches;

	// Write Hours to the first column of the grid
	for(sheetLine = 1; sheetLine <= linesLength; ++sheetLine) {
		cellName = xlsx.utils.encode_cell({
			c: sheetColumn,
			r: sheetLine
		})
		sheet[cellName] = {
			v: eventHours[sheetLine - 1],
			t: 's'
		}
	}

	for(sheetColumn = 1; sheetColumn <= columnsLength; ++sheetColumn) {
		currentCourt = courts[sheetColumn - 1];
		courtMatches = currentCourt.matches;
		matchIndex   = 0;

		sheet[
			xlsx.utils.encode_cell({
				c: sheetColumn,
				r: 0
			})
		] = {
			v: currentCourt.court_name,
			t: 's'
		}

		for(sheetLine = 1; sheetLine <= linesLength; ++sheetLine) {
			currentTime = eventDateData.match_hours[sheetLine - 1];
			currentMatch = courtMatches[matchIndex];

			if((currentMatch && currentMatch.start_hour) === currentTime) {
				cellName = xlsx.utils.encode_cell({
					c: sheetColumn,
					r: sheetLine
				})

				sheet[cellName] = {
					v: `${$INCLUDE_MATCH_CODES ? `${currentMatch.match_name}\n` : ''}${currentMatch.officials_list || ''}`,
					t: 's'
				};
				matchIndex++;
			}
		}
	}

	sheet['!ref'] = xlsx.utils.encode_range({
		s: {
			c: 0, 
			r: 0
		}, 
		e: {
			c: columnsLength, 
			r: linesLength
		}
	})

	return {
		name: eventDateData.day,
		data: sheet
	}
}

function __generateSheet (courts, eventDateData) {
	var sheet 			= {},
		sheetLine 		= 0,
		sheetColumn 	= 0, 
		columnsLength 	= eventDateData.match_hours.length,
		matchIndex 		= 0,
		currentMatch,
		currentCourt,
		currentTime, 
		cell,
		cellName;

	// Write Matches Hours (first line)
	for(sheetColumn = 1; sheetColumn <= columnsLength; ++sheetColumn) {
		cell = {
			v: eventDateData.match_hours[sheetColumn - 1],
			t: 's'
		}
		cellName = xlsx.utils.encode_cell({
			c: sheetColumn,
			r: sheetLine
		})
		sheet[cellName] = cell;
	}

	for(sheetLine = 1; sheetLine <= courts.length; ++sheetLine) {
		matchIndex = 0;
		// put court name to the first column
		currentCourt = courts[sheetLine - 1];
		cell = {
			v: currentCourt.court_name,
			t: 's',
			s: {
				alignment: {
					horizontal: 'center'
				},
				font: {
					bold: true
				}
			}
		}
		cellName = xlsx.utils.encode_cell({
			c: 0,
			r: sheetLine
		})
		sheet[cellName] = cell;
		// matches content
		for(sheetColumn = 1; sheetColumn <= columnsLength; ++sheetColumn) {
			currentMatch = currentCourt.matches[matchIndex];
			currentTime = eventDateData.match_hours[sheetColumn - 1];

			if((currentMatch && currentMatch.start_hour) === currentTime) {
				cell = {
					v: `${currentMatch.match_name}\n${currentMatch.officials_list || ''}`,
					t: 's'
				}
				cellName = xlsx.utils.encode_cell({
					c: sheetColumn,
					r: sheetLine
				})
				sheet[cellName] = cell;
				matchIndex++;
			}
		}
	}
	sheet['!ref'] = xlsx.utils.encode_range({
		s: {
			c: 0, 
			r: 0
		}, 
		e: {
			c: columnsLength, 
			r: courts.length
		}
	})
	return {
		name: eventDateData.day,
		data: sheet
	}
}

const __COURTS_MATCHES_SQL =
`SELECT  c.name court_name, (
    SELECT COALESCE(ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON(ctmatches))), '[]'::JSON) 
    FROM ( 
        SELECT  
            FORMAT('%s %s', d.short_name, m.display_name) "match_name", 
            TO_CHAR(m.secs_start, 'HH12:MI AM') "start_hour", ( 
                SELECT 
                	--COALESCE(ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON(o))), '[]'::JSON) 
                	string_agg(o."name", '\n')
                FROM ( 
                    SELECT COALESCE(eo.schedule_name, FORMAT('%s %s', u.first, u.last)) "name" 
                    FROM "event_official_schedule" eos  
                    LEFT JOIN "event_official" eo 
                        ON eo.event_official_id = eos.event_official_id 
                    LEFT JOIN "official" o 
                        ON o.official_id = eo.official_id 
                    LEFT JOIN "user" u 
                        ON u.user_id = o.user_id 
                    WHERE eos.match_name = m.display_name 
                        AND eos.division_id = m.division_id 
                        AND eos.published = true
                    ORDER BY eos.ref_num 
                ) "o" 
            ) "officials_list", 
            m.officials_assigned 
        FROM matches m 
        LEFT JOIN division d  
            ON d.division_id = m.division_id 
        WHERE m.event_id = mt.event_id   
            AND m.court_id = mt.court_id   
            AND TO_CHAR(m.secs_start, 'YYYY-MM-DD') = $2
        ORDER BY m.secs_start   
    ) "ctmatches"  
 ) "matches" 
FROM matches mt   
LEFT JOIN courts c  
    ON mt.court_id = c.uuid
left join "event" e 
    on e.event_id = mt.event_id 
WHERE mt.event_id = $1  
	AND TO_CHAR(mt.secs_start, 'YYYY-MM-DD') = $2
GROUP BY e.event_id, mt.event_id, mt.court_id, c.name, c.sort_priority  
ORDER BY c.sort_priority`;
