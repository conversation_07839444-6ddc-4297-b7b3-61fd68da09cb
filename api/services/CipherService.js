'use strict';

const crypto = require('crypto');

class CipherService {
    constructor() {};

    get _USER_ID_ENCRYPTION_ALG() {
        return 'aes-256-ctr';
    }

    get _ENCYPTION_SALT() {
        return 'gqhEf0I3syg330BR';
    }

    encryptUserID(id) {

        let cypher = crypto.createCipher(this._USER_ID_ENCRYPTION_ALG, this._ENCYPTION_SALT);

        let hash = cypher.update(`${id}-${this._ENCYPTION_SALT}`, 'utf8', 'hex');

        hash += cypher.final('hex');

        return hash;
    }

    decryptUserID(hash) {
        let decypher = crypto.createDecipher(this._USER_ID_ENCRYPTION_ALG, this._ENCYPTION_SALT);

        let userID = decypher.update(hash, 'hex', 'utf8');

        userID += decypher.final('utf8');

        let _splitted = userID.split('-');

        userID = +_splitted[0];

        return userID;
    }

    encryptStripeToken(data) {
        let cypher = crypto.createCipher(this._USER_ID_ENCRYPTION_ALG, this._ENCYPTION_SALT);

        let token = cypher.update(JSON.stringify(data), 'utf8', 'hex');

        token += cypher.final('hex');

        return token;
    }

    decryptStripeToken(token) {
        let decypher = crypto.createDecipher(this._USER_ID_ENCRYPTION_ALG, this._ENCYPTION_SALT);

        let data = null;

        try {
            data = decypher.update(token, 'hex', 'utf8');
            data += decypher.final('utf8');
        } catch (err) {
            data = null;
        }

        let result = null;

        if (data) {
            try {
                result = JSON.parse(data);
            } finally {
                return result;
            }
        }
    }
}

module.exports = new CipherService();
