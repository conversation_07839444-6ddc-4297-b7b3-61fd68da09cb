SELECT
  SUM(d."teams") "Teams",
  TO_CHAR(SUM(d."total_sw_fee"), '$FM999G999.0099') "SW Fee",
  TO_CHAR(SUM(d."real_collected"), '$FM999G999.0099') "Collected"
FROM (
  SELECT 
      d."event_id" "id",
      d."long_name" "Event Name",
      d."teams_qty" "teams",
      d."sw_fee" "per_team_fee",
      (d."teams_qty" * d."sw_fee") "total_sw_fee",
      d."real_collected"
  FROM (
      SELECT 
         e."event_id", e."long_name",e."date_start", e."season", 
         COALESCE(e."teams_entry_sw_fee", 0) "sw_fee"
         , SUM(
             (
              SELECT 
                  COUNT(rt."roster_team_id") 
              FROM "roster_team" rt 
              WHERE rt."event_id" = e."event_id"
                  AND rt."deleted" IS NULL 
                  AND (
                      rt."status_entry" = 12 OR 
                      rt."status_paid" = 22
                  )
             )
         ) "teams_qty" 
         , COALESCE(SUM(
             (
              SELECT SUM(COALESCE(p."collected_sw_fee", 0) + COALESCE(p."additional_fee_amount", 0))
              FROM "purchase" p 
              WHERE p."event_id" = e."event_id"
                  AND p."type" IN ('card', 'ach')
                  AND p."status" = 'paid'
                  AND p."payment_for" = 'teams'
                  AND p."stripe_payment_type" = 'connect'
             )
         ), 0) "real_collected"
      FROM "event" e 
      WHERE e."date_start"::DATE BETWEEN '2016-09-01'::DATE and '2017-09-01'::DATE
         AND NOT (e."is_test" IS TRUE)
         AND e."season" = 2017
      GROUP BY e."event_id", e."long_name", e."date_start", e."season"
      ORDER BY "event_id"
   ) "d"
  WHERE "d"."teams_qty" > 0
) "d"