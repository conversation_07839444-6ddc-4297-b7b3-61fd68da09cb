{"apps": [{"name": "app-prod-stage", "script": "app.js", "instances": 2, "exec_mode": "cluster", "log_date_format": "YYYY-MM-DD HH:mm:ss Z", "color": false, "args": "--dev --staging --port 3003", "max_restarts": 10, "restart_delay": 5000, "kill_timeout": 10000, "wait_ready": true, "listen_timeout": 20000}, {"name": "scheduler-worker-stage", "script": "scheduler/scheduler.worker.js", "args": "--dev", "exec_mode": "fork", "log_date_format": "YYYY-MM-DD HH:mm:ss Z", "color": false, "max_restarts": 10, "restart_delay": 5000, "kill_timeout": 10000, "env": {"NODE_ENV": "development"}}, {"name": "workers-queue-stage", "script": "worker/main.js", "args": "--dev", "exec_mode": "fork", "log_date_format": "YYYY-MM-DD HH:mm:ss Z", "color": false, "max_restarts": 10, "restart_delay": 1000, "kill_timeout": 10000, "env": {"NODE_ENV": "development"}}]}