var argv 				= require('optimist').argv,
	xlsx    			= require('xlsx'),
	pg      			= require('pg'),
	Db 					= require('./utils/DbService'),
	_       			= require('lodash'),
	LINE_BREAK      	= require('os').EOL;

var	filePath 			= argv.path,
	contentStartLine 	= argv.start_line || 0
	masterClubId 		= argv.club_id,
	clubOwnerId			= argv.owner_id,
	currentSeason 		= argv.season,
	dbConnBase64 		= argv.connection;

// TODO add validation
