5) SportWrench App RateLimiterService - Unit Tests (deterministic, no Redis) createRateLimiter basics logs and proceeds when key selector throws:
     AssertionError: expected undefined to be true
      at doAsserterAsyncAndAddThen (node_modules/chai-as-promised/lib/chai-as-promised.js:293:29)
      at Assertion.<anonymous> (node_modules/chai-as-promised/lib/chai-as-promised.js:283:21)
      at Assertion.get (node_modules/chai/lib/chai/utils/overwriteProperty.js:50:37)
      at Context.<anonymous> (test/services-tests/RateLimiterService.focused.spec.js:160:65)
      at runMicrotasks (<anonymous>)
      at processTicksAndRejections (node:internal/process/task_queues:96:5)

  6) SportWrench App RateLimiterService - Unit Tests (deterministic, no Redis) Utility methods getKeySelectorSafe: logs and returns null on error:
     AssertionError: expected undefined to be true
      at doAsserterAsyncAndAddThen (node_modules/chai-as-promised/lib/chai-as-promised.js:293:29)
      at Assertion.<anonymous> (node_modules/chai-as-promised/lib/chai-as-promised.js:283:21)
      at Assertion.get (node_modules/chai/lib/chai/utils/overwriteProperty.js:50:37)
      at Context.<anonymous> (test/services-tests/RateLimiterService.focused.spec.js:236:65)

  7) SportWrench App RateLimiterService _initClient() should initialize Redis client successfully:
     AssertionError: expected false to be true
      at doAsserterAsyncAndAddThen (node_modules/chai-as-promised/lib/chai-as-promised.js:293:29)
      at Assertion.<anonymous> (node_modules/chai-as-promised/lib/chai-as-promised.js:283:21)
      at Assertion.get (node_modules/chai/lib/chai/utils/overwriteProperty.js:50:37)
      at Context.<anonymous> (test/services-tests/RateLimiterService.spec.js:95:101)
      at runMicrotasks (<anonymous>)
      at processTicksAndRejections (node:internal/process/task_queues:96:5)

  8) SportWrench App RateLimiterService _initClient() should handle Redis connection failure gracefully:
     AssertionError: expected true to be false
      at doAsserterAsyncAndAddThen (node_modules/chai-as-promised/lib/chai-as-promised.js:293:29)
      at Assertion.<anonymous> (node_modules/chai-as-promised/lib/chai-as-promised.js:283:21)
      at Assertion.get (node_modules/chai/lib/chai/utils/overwriteProperty.js:50:37)
      at Context.<anonymous> (test/services-tests/RateLimiterService.spec.js:103:33)
      at runMicrotasks (<anonymous>)
      at processTicksAndRejections (node:internal/process/task_queues:96:5)

  9) SportWrench App RateLimiterService _getKeySelectorSafe() should return null and log error on key selector failure:
     AssertionError: expected false to be true
      at doAsserterAsyncAndAddThen (node_modules/chai-as-promised/lib/chai-as-promised.js:293:29)
      at Assertion.<anonymous> (node_modules/chai-as-promised/lib/chai-as-promised.js:283:21)
      at Assertion.get (node_modules/chai/lib/chai/utils/overwriteProperty.js:50:37)
      at Context.<anonymous> (test/services-tests/RateLimiterService.spec.js:166:108)

  10) SportWrench App RateLimiterService createRateLimiter() should proceed when Redis client is not ready (fail-open):
     AssertionError: expected true to be false
      at doAsserterAsyncAndAddThen (node_modules/chai-as-promised/lib/chai-as-promised.js:293:29)
      at Assertion.<anonymous> (node_modules/chai-as-promised/lib/chai-as-promised.js:283:21)
      at Assertion.get (node_modules/chai/lib/chai/utils/overwriteProperty.js:50:37)
      at test/services-tests/RateLimiterService.spec.js:227:53
      at runMicrotasks (<anonymous>)
      at processTicksAndRejections (node:internal/process/task_queues:96:5)

  11) SportWrench App RateLimiterService createRateLimiter() should handle soft timeout gracefully:
     AssertionError: expected false to be true
      at doAsserterAsyncAndAddThen (node_modules/chai-as-promised/lib/chai-as-promised.js:293:29)
      at Assertion.<anonymous> (node_modules/chai-as-promised/lib/chai-as-promised.js:283:21)
      at Assertion.get (node_modules/chai/lib/chai/utils/overwriteProperty.js:50:37)
      at Context.<anonymous> (test/services-tests/RateLimiterService.spec.js:337:99)

  12) SportWrench App RateLimiterService createRateLimiter() should handle general errors gracefully (fail-open):
     AssertionError: expected false to be true
      at doAsserterAsyncAndAddThen (node_modules/chai-as-promised/lib/chai-as-promised.js:293:29)
      at Assertion.<anonymous> (node_modules/chai-as-promised/lib/chai-as-promised.js:283:21)
      at Assertion.get (node_modules/chai/lib/chai/utils/overwriteProperty.js:50:37)
      at Context.<anonymous> (test/services-tests/RateLimiterService.spec.js:367:110)
      at runMicrotasks (<anonymous>)
      at processTicksAndRejections (node:internal/process/task_queues:96:5)

  13) SportWrench App RateLimiterService Configuration Options should use default blockDuration when not specified:
     AssertionError: expected false to be true
      at doAsserterAsyncAndAddThen (node_modules/chai-as-promised/lib/chai-as-promised.js:293:29)
      at Assertion.<anonymous> (node_modules/chai-as-promised/lib/chai-as-promised.js:283:21)
      at Assertion.get (node_modules/chai/lib/chai/utils/overwriteProperty.js:50:37)
      at Context.<anonymous> (test/services-tests/RateLimiterService.spec.js:419:49)
      at runMicrotasks (<anonymous>)
      at processTicksAndRejections (node:internal/process/task_queues:96:5)

  14) SportWrench App RateLimiterService Configuration Options should use default execEvenly when not specified:
     AssertionError: expected false to be true
      at doAsserterAsyncAndAddThen (node_modules/chai-as-promised/lib/chai-as-promised.js:293:29)
      at Assertion.<anonymous> (node_modules/chai-as-promised/lib/chai-as-promised.js:283:21)
      at Assertion.get (node_modules/chai/lib/chai/utils/overwriteProperty.js:50:37)
      at Context.<anonymous> (test/services-tests/RateLimiterService.spec.js:461:49)
      at runMicrotasks (<anonymous>)
      at processTicksAndRejections (node:internal/process/task_queues:96:5)

  15) SportWrench App US Bank Account Payments Requires action -> processing -> success flow should successfully process ACH payment:
     Error: channel closed
      at ConfirmChannel.<anonymous> (node_modules/amqplib/lib/channel.js:43:14)
      at ConfirmChannel.emit (node:events:525:35)
      at ConfirmChannel.emit (node:domain:489:12)
      at ConfirmChannel.toClosed (node_modules/amqplib/lib/channel.js:158:10)
      at Connection._closeChannels (node_modules/amqplib/lib/connection.js:337:20)
      at Connection.toClosed (node_modules/amqplib/lib/connection.js:344:10)
      at Connection.onSocketError (node_modules/amqplib/lib/connection.js:308:12)
      at Connection.emit (node:events:513:28)
      at Connection.emit (node:domain:489:12)
      at Socket.go (node_modules/amqplib/lib/connection.js:434:14)
      at Socket.emit (node:events:513:28)
      at Socket.emit (node:domain:489:12)
      at emitReadable_ (node:internal/streams/readable:578:12)
      at processTicksAndRejections (node:internal/process/task_queues:82:21)

  16) SportWrench App US Bank Account Payments Requires action -> processing -> failed flow should successfully fail ACH payment:
     Error: channel closed
      at ConfirmChannel.<anonymous> (node_modules/amqplib/lib/channel.js:43:14)
      at ConfirmChannel.emit (node:events:525:35)
      at ConfirmChannel.emit (node:domain:489:12)
      at ConfirmChannel.toClosed (node_modules/amqplib/lib/channel.js:158:10)
      at Connection._closeChannels (node_modules/amqplib/lib/connection.js:337:20)
      at Connection.toClosed (node_modules/amqplib/lib/connection.js:344:10)
      at Connection.onSocketError (node_modules/amqplib/lib/connection.js:308:12)
      at Connection.emit (node:events:513:28)
      at Connection.emit (node:domain:489:12)
      at Socket.go (node_modules/amqplib/lib/connection.js:434:14)
      at Socket.emit (node:events:513:28)
      at Socket.emit (node:domain:489:12)
      at emitReadable_ (node:internal/streams/readable:578:12)
      at processTicksAndRejections (node:internal/process/task_queues:82:21)

  17) SportWrench App Volley Station API Tests Authentication Policy should authenticate valid API key:
     AssertionError: expected false to be true
      at doAsserterAsyncAndAddThen (node_modules/chai-as-promised/lib/chai-as-promised.js:293:29)
      at Assertion.<anonymous> (node_modules/chai-as-promised/lib/chai-as-promised.js:283:21)
      at Assertion.get (node_modules/chai/lib/chai/utils/overwriteProperty.js:50:37)
      at Context.<anonymous> (test/services-tests/VolleyStationAPI.spec.js:76:46)
      at processImmediate (node:internal/timers:466:21)
      at process.topLevelDomainCallback (node:domain:161:15)
      at process.callbackTrampoline (node:internal/async_hooks:128:24)

  18) SportWrench App Volley Station API Tests Authentication Policy should generate different client IDs for different API keys:
     AssertionError: expected undefined to not equal undefined
      at Context.<anonymous> (test/services-tests/VolleyStationAPI.spec.js:144:51)
      at processImmediate (node:internal/timers:466:21)
      at process.topLevelDomainCallback (node:domain:161:15)
      at process.callbackTrampoline (node:internal/async_hooks:128:24)

  19) SportWrench App Volley Station API Tests Authentication Policy should use SHA256 hash for client ID generation:
     AssertionError: expected undefined to equal '75ad81f5'
      at Context.<anonymous> (test/services-tests/VolleyStationAPI.spec.js:161:46)
      at processImmediate (node:internal/timers:466:21)
      at process.topLevelDomainCallback (node:domain:161:15)
      at process.callbackTrampoline (node:internal/async_hooks:128:24)

  20) SportWrench App Volley Station API Tests Authentication Policy should preserve existing user object properties:
     AssertionError: expected false to be true
      at doAsserterAsyncAndAddThen (node_modules/chai-as-promised/lib/chai-as-promised.js:293:29)
      at Assertion.<anonymous> (node_modules/chai-as-promised/lib/chai-as-promised.js:283:21)
      at Assertion.get (node_modules/chai/lib/chai/utils/overwriteProperty.js:50:37)
      at Context.<anonymous> (test/services-tests/VolleyStationAPI.spec.js:174:46)
      at processImmediate (node:internal/timers:466:21)
      at process.topLevelDomainCallback (node:domain:161:15)
      at process.callbackTrampoline (node:internal/async_hooks:128:24)

  21) SportWrench App Volley Station API Tests Rate Limiting Policies should create read rate limiter with correct configuration:

      AssertionError: expected { Object (points, duration, ...) } to deeply equal { Object (points, duration, ...) }
      + expected - actual

       {
      -  "duration": 5
      -  "enableSoftTimeout": true
      -  "execEvenly": true
      -  "points": 25
      -  "softTimeoutMs": 1000
      +  "blockDuration": 300
      +  "duration": 60
      +  "points": 100
       }

      at Assertion.assertEqual (node_modules/chai/lib/chai/core/assertions.js:485:19)
      at Assertion.ctx.<computed> (node_modules/chai/lib/chai/utils/addMethod.js:41:25)
      at doAsserterAsyncAndAddThen (node_modules/chai-as-promised/lib/chai-as-promised.js:293:29)
      at Assertion.<anonymous> (node_modules/chai-as-promised/lib/chai-as-promised.js:252:17)
      at Assertion.ctx.<computed> [as equal] (node_modules/chai/lib/chai/utils/overwriteMethod.js:49:33)
      at Context.<anonymous> (test/services-tests/VolleyStationAPI.spec.js:218:36)
      at processImmediate (node:internal/timers:466:21)
      at process.topLevelDomainCallback (node:domain:161:15)
      at process.callbackTrampoline (node:internal/async_hooks:128:24)

  22) SportWrench App Volley Station API Tests Rate Limiting Policies should create write rate limiter with correct configuration:

      AssertionError: expected { Object (points, duration, ...) } to deeply equal { Object (points, duration, ...) }
      + expected - actual

       {
      -  "duration": 5
      -  "enableSoftTimeout": true
      -  "execEvenly": true
      -  "points": 25
      -  "softTimeoutMs": 1000
      +  "blockDuration": 300
      +  "duration": 60
      +  "points": 100
       }

      at Assertion.assertEqual (node_modules/chai/lib/chai/core/assertions.js:485:19)
      at Assertion.ctx.<computed> (node_modules/chai/lib/chai/utils/addMethod.js:41:25)
      at doAsserterAsyncAndAddThen (node_modules/chai-as-promised/lib/chai-as-promised.js:293:29)
      at Assertion.<anonymous> (node_modules/chai-as-promised/lib/chai-as-promised.js:252:17)
      at Assertion.ctx.<computed> [as equal] (node_modules/chai/lib/chai/utils/overwriteMethod.js:49:33)
      at Context.<anonymous> (test/services-tests/VolleyStationAPI.spec.js:242:36)
      at processImmediate (node:internal/timers:466:21)
      at process.topLevelDomainCallback (node:domain:161:15)
      at process.callbackTrampoline (node:internal/async_hooks:128:24)

  23) SportWrench App Volley Station API Tests Event Access Policy should handle service errors gracefully:
     AssertionError: expected false to be true
      at doAsserterAsyncAndAddThen (node_modules/chai-as-promised/lib/chai-as-promised.js:293:29)
      at Assertion.<anonymous> (node_modules/chai-as-promised/lib/chai-as-promised.js:283:21)
      at Assertion.get (node_modules/chai/lib/chai/utils/overwriteProperty.js:50:37)
      at Context.<anonymous> (test/services-tests/VolleyStationAPI.spec.js:370:62)
