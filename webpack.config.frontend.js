const path = require('node:path');
const glob = require('glob');
const CopyWebpackPlugin = require('copy-webpack-plugin');
const HtmlWebpackPlugin = require('html-webpack-plugin');

const VENDOR_ENTRIES = ['./frontend/vendor.js'];

module.exports = {
    extends: path.resolve(__dirname, './webpack.config.base.js'),

    entry: {
        vendor: VENDOR_ENTRIES,
        main: glob.sync([
            './frontend/sport-wrench.module.js',
            './frontend/**/*.js',
        ], { absolute: true, ignore: VENDOR_ENTRIES }),
    },

    plugins: [
        new HtmlWebpackPlugin({
            template: './frontend/sport-wrench.html',
            filename: 'index.html',
            inject: false,
            minify: false,
        }),
        new CopyWebpackPlugin({
            patterns: [
                { context: './frontend/',  from: '**/*.html',  to: '[path][name][ext]' },
            ],
        }),
    ],

    devServer: {
        port: 8079,
    },
};
