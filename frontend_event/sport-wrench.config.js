angular.module('SportWrench')

.constant('SOCIAL_PATH', '/images/social_networks/')

.constant('SOCIAL_NETWORKS', {
    facebook:   { img: 'facebook_icon.png',     type: 'url',    name: 'Facebook' },
    instagram:  { img: 'instagram_icon.png',    type: 'url',    name: 'Instagram' },
    twitter:    { img: 'twitter_icon.png',      type: 'url',    name: 'Twitter' },
    snapchat:   { img: 'snapchat_icon.png',     type: 'url',   name: 'Snapchat' }
})

.run([
    '$rootScope', '$templateCache', '$state', '$location', '$modalStack', 'previousStateService', '$localStorage', '$http',
    function($rootScope, $templateCache, $state, $location, $modalStack, previousStateService, $localStorage, $http)
{

    $localStorage.allowLastEventLoading = true;

    $rootScope.$on('$stateChangeSuccess', function (ev, to, toParams, from, fromParams) {
        for (var i=0; i < $rootScope.$storage.favorites.length; i++) {
            if ($rootScope.$storage.favorites[i].url === $location.$$url) {
                if ($rootScope.$storage.favorites[i].type) {
                    $rootScope[$rootScope.$storage.favorites[i].type] = $rootScope.$storage.favorites[i].level3;
                }

                $rootScope.isFavorite = true;
                break;
            } else {
                $rootScope.isFavorite = false;
            }
        }

        gtag('event', 'page_view', {
            page_title: to.name,
            page_location: $location.absUrl()
        });

        previousStateService.set(from.name, fromParams);
    });

    $rootScope.$on('$stateChangeStart', function() {                
        var top = $modalStack.getTop();
           
        if (top) {
            $modalStack.dismiss(top.key);
        }
    });

    __fetchCsrfToken($http);
}])

.config([
    '$httpProvider', '$uiViewScrollProvider', 'cfpLoadingBarProvider', '$compileProvider', '$provide', '$validationProvider',
    function($httpProvider, $uiViewScrollProvider, cfpLoadingBarProvider, $compileProvider, $provide, $validationProvider)
{

    $compileProvider.debugInfoEnabled(false);
    
    cfpLoadingBarProvider.latencyThreshold = 500;

    $validationProvider
    .setExpression({
      range: function(value, scope, element, attrs) {
        if (value >= parseInt(attrs.min) && value <= parseInt(attrs.max)) {
          return value;
        }
      }
    })
    .setDefaultMsg({
      range: {
        error: '',
        success: 'good'
      }
    });

    $httpProvider.defaults.useXDomain = true;
    delete $httpProvider.defaults.headers.common['X-Requested-With'];

    $uiViewScrollProvider.useAnchorScroll(); 

    $httpProvider.interceptors.push(function ($rootScope, $q, $location) {
        var _re             = /^\/api\/esw\/[0-9|a-z]{1,}$/,
            _serverBusy     = 'The server is busy. Please try again later',
            _requestError   = 'Request Error';
        return {
            response: function (response) {
                if(_re.test(response.config.url)) {
                    $rootScope.$emit('eventDataLoaded', response.data);
                }
                return response || $q.when(response);
            },
            responseError: function (rejection) {
                var _msg, _type;
                if(rejection) {
                    switch(rejection.status) {
                        case 502:
                            _msg = _serverBusy;
                            _type = 'warning';
                            break;
                        case 400:
                            _msg = (rejection.data && rejection.data.validation) || _requestError;
                            _type = 'danger'
                            break;
                        default:
                            _msg = _requestError;
                            _type = 'warning'
                            break
                    }
                    $rootScope.$emit('createNotification', _msg, _type);
                }
                return $q.reject(rejection)
            },
            request: function (config) {
                config.headers["sw-referer"] = $location.absUrl();
                if($location.search().clean && config.url.indexOf('/api/esw') >= 0)
                    config.url += '?clean=true';                
                return config;
            }
        }
    })

    // Reload page on UI updates
    $httpProvider.interceptors.push(function ($injector, $timeout, $window, $q) {
        var cacheAnswer = null, 
            runTimeout = function () {
                $timeout(function () {
                    cacheAnswer = null;
                }, 60 * 1000)
            },
            confirm_message = 'Site has been updated. You need to reload page. Reload page now?'
        return {
            response: function(response) { 
                if(!response) return $q.when(response)             
                if(cacheAnswer !== null) return response || $q.when(response);                
                var response_hash = response.headers()['content-updated-esw'], contentHashService, local_hash;
                if(response_hash) {
                    response_hash = response_hash.split('-')[0];
                    contentHashService = $injector.get('contentHashService');
                    local_hash = contentHashService.get();
                    if(!local_hash) {
                        contentHashService.set(response_hash);
                    } else if(response_hash !== local_hash) {
                        cacheAnswer = $window.confirm(confirm_message)
                        if(cacheAnswer) {  
                            contentHashService.set(response_hash);                  
                            $window.location.reload(true);
                        } else {
                            runTimeout();
                        }
                    }
                }
                return response;
            }
        };
    });

    $httpProvider.defaults.headers.post[ 'Content-Type' ] = 'application/json;charset=utf-8';
    $httpProvider.defaults.headers.put[ 'Content-Type' ] = 'application/json;charset=utf-8';
    $httpProvider.defaults.headers.get = {
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache',
        'Content-Type': 'application/json'
    };
}]);

function __fetchCsrfToken($http) {
    const methods = ['post', 'put', 'patch', 'delete'];
    $http.get('/api/csrfToken')
        .success(function ({_csrf}) {
            for(const method of methods) {
                if(!$http.defaults.headers[method]) {
                    // DELETE method doesn't exist in $http.defaults.headers
                    // https://code.angularjs.org/1.5.6/docs/api/ng/provider/$httpProvider
                    $http.defaults.headers[method] = {
                        'X-CSRF-Token': null
                    };
                }

                $http.defaults.headers[method]['X-CSRF-Token'] = _csrf;
            }
        });
}
