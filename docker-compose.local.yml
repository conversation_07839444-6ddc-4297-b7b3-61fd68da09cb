version: '3.8'

services:
    postgres:
        image: postgres:15
        container_name: sw-db
        environment:
            POSTGRES_USER: postgres
            POSTGRES_PASSWORD: postgres
            POSTGRES_DB: sw_dev
            TZ: UTC
            PGTZ: UTC
        ports:
            - "5432:5432"
        volumes:
            - postgres_data:/var/lib/postgresql/data

    redis:
        image: redis:3.2.11
        ports:
            - "6379:6379"

volumes:
    postgres_data:
