{"apps": [{"name": "app-prod", "script": "app.js", "instances": 2, "exec_mode": "cluster", "log_date_format": "YYYY-MM-DD HH:mm:ss Z", "color": false, "args": "--prod", "max_restarts": 10, "restart_delay": 1000, "kill_timeout": 10000, "wait_ready": true, "listen_timeout": 20000}, {"name": "scheduler-worker-prod", "script": "scheduler/scheduler.worker.js", "args": "--prod", "exec_mode": "fork", "log_date_format": "YYYY-MM-DD HH:mm:ss Z", "color": false, "max_restarts": 10, "restart_delay": 1000, "kill_timeout": 10000}, {"name": "workers-queue-prod", "script": "worker/main.js", "args": "--prod", "exec_mode": "fork", "log_date_format": "YYYY-MM-DD HH:mm:ss Z", "color": false, "max_restarts": 10, "restart_delay": 1000, "kill_timeout": 10000}]}