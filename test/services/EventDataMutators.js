const CONSTRAINT_TYPES = require('../constants/CONSTRAINT_TYPES');
const EVENT_TABLES = require('../constants/EVENT_TABLES');
const FOREIGN_CONSTRAINTS = require('../constants/FOREIGN_CONSTRAINTS');
const dataMutationCreators = [
    setEventId,
    resolveFKDependencies,
    clearPrimaryKey,
];

const UNRESOLVABLE_ROW_DEPENDENCY_ERROR = Symbol('UNRESOLVABLE_ROW_DEPENDENCY_ERROR');

module.exports = {
    UNRESOLVABLE_ROW_DEPENDENCY_ERROR,
    createRowMutator(eventData, tableName, idKey) {
        const dataMutators = dataMutationCreators
            .map(m => m.call(eventData, tableName, idKey))
            .filter(v => _.isFunction(v));

        return (row) => {
            for(const dataMutator of dataMutators) {
                dataMutator(row);
            }

            return row;
        };
    }
};


/**
 * @this EventData
 * @param {string} tableName
 * @param {TableConstraint} idKey
 * @returns {function}
 */
function setEventId(tableName, idKey) {
    const hasEventColumn = EVENT_TABLES.includes(tableName);
    if(!hasEventColumn) {
        return undefined;
    }
    return (row) => {
        row.event_id = this.eventId;
    };
}

/**
 * @this EventData
 * @param {string} tableName
 * @param {TableConstraint} idKey
 * @returns {function}
 */
function clearPrimaryKey(tableName, idKey) {
    if(idKey.type !== CONSTRAINT_TYPES.primaryKey) {
        return undefined;
    }
    return (row) => {
        for(const column of idKey.columns) {
            if(row.hasOwnProperty(column)) {
                delete row[column];
            }
        }
    };
}

/**
 * @this EventData
 * @param {string} tableName
 * @param {TableConstraint} idKey
 * @returns {function}
 */
function resolveFKDependencies(tableName, idKey) {
    const constraints = FOREIGN_CONSTRAINTS.byForeign(tableName);
    if(constraints.length === 0) {
        return undefined;
    }
    return (row) => {
        for(const [[primaryTable, primaryKey], [, foreignKey]] of constraints) {
            const oldKey = row[foreignKey];
            if(oldKey) {
                // TODO: search by primaryKey variable value if it is not a primary key of primaryTable
                const linkedRow = this.findByPK(primaryTable, oldKey, null);
                if(!linkedRow) {
                    throw UNRESOLVABLE_ROW_DEPENDENCY_ERROR;
                }
                row[foreignKey] = linkedRow[primaryKey];
            }
        }
    };
}
