'use strict';

const
	fs 				= require('mz/fs'),
	loader 			= require('./testLoader'),
	sails 			= require('sails'),
	squel 			= require('./squel'),
	_ 				= require('lodash'),
	chai 			= require('chai'),
	chaiAsPromised 	= require('chai-as-promised'),
	sinon 			= require('sinon'),
	utils           = require('./../api/lib/swUtils'),
	knex			= require('knex')({client: 'pg'});

chai.use(chaiAsPromised);

const sailsConfig = require('./config/config');

const 
	settingsContent 			= require('./fixture/settings.row.json'),
	stripeAccountContent 		= require('./fixture/stripe_account.row.json'),
	testStripeAccountContent 	= require('./fixture/stripe_account.test.row.json');

global.HOST 	= `localhost:${sailsConfig.port}`;
global._ 		= _;
global.squel 	= squel;
global.expect 	= chai.expect;
global.settings = settingsContent;
global.sinon 	= sinon;
global.utils    = utils;
global.knex 	= knex;


describe('SportWrench App', () => {
	before(function () {
		this.timeout(10000);
		return liftServer().then(() => fillDb());
	});

	after(() => {
		return clearDb().then(() => lowerServer());
	});

	it('Should do nothing', () => {
	});

	loader(testsGenerator('api-tests'));
	loader(testsGenerator('stripe'));
	loader(testsGenerator('services-tests'));
	loader(testsGenerator('utils-tests'));
    loader(testsGenerator('scheduler'));
    loader(testsGenerator('ticket-refunds'));
})

function* testsGenerator (folderName) {
	let folderFiles = fs.readdirSync(`${__dirname}/${folderName}`);
	yield* folderFiles.map(fileName => {
		return `${folderName}/${fileName}`
	})
}

function liftServer () {
	return new Promise (function liftServer (resolve, reject) {
		sails.lift(sailsConfig, function (err, sails) {
			if(err) {
				reject(err)
			} else {
				let _conn = sails.config.connections.test;

				if (_conn.host !== sailsConfig.connections.test.host) {
					reject(new Error(`Host "${_conn}" not equals to "${sailsConfig.connections.test.host}"`))
				} else  {
					console.info(`Connected to "${_conn.database}" database on "${_conn.host}:${_conn.port}"`);

					global.sails = sails;
					resolve(sails)
				}
			}
		});
	})
}

function lowerServer () {
	return new Promise(function lowerServer (resolve) {
		sails.lower(() => process.exit());
		resolve();
	})
}

function fillDb () {
	return Promise.all([
		Db.query(squel.insert().into('settings').setFieldsRows(settingsContent).toString()),
        Db.query(squel.insert().into('stripe_account').setFields(stripeAccountContent).toString()),
        Db.query(squel.insert().into('stripe_account').setFields(testStripeAccountContent).toString())
	]);
}

function clearDb () {
	return Promise.all([
        Db.query('DELETE FROM "settings";'),
        Db.query('DELETE FROM "stripe_account";')
	])
}
