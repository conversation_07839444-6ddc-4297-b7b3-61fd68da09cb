#!/usr/bin/env node

'use strict';

const path = require('path');
const Mocha = require('mocha');

// Validate environment
if (!process.env.REDIS_URL) {
    console.warn('Warning: REDIS_URL not set, using default redis://localhost:6379');
}

console.log('Running Volley Station API tests in isolation...');
console.log('Redis URL:', process.env.REDIS_URL || 'redis://localhost:6379');
console.log('');

// Create Mocha instance
const mocha = new Mocha({
    timeout: 10000,
    reporter: 'spec',
    color: true
});

// Add the volley station test file
mocha.addFile(path.join(__dirname, 'services-tests', 'VolleyStationAPI.spec.js'));

// Run the tests
mocha.run(function(failures) {
    if (failures === 0) {
        console.log('\nAll Volley Station API tests passed!');
        process.exit(0);
    } else {
        console.log(`\n${failures} test(s) failed`);
        process.exit(1);
    }
});
