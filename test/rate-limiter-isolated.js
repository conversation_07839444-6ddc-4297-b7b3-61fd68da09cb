#!/usr/bin/env node

/**
 * Isolated Rate Limiter Test Runner
 *
 * This script runs only the RateLimiterService tests in isolation
 * without requiring the full test suite or database connection.
 *
 * Usage:
 *   node test/rate-limiter-isolated.js
 *   npm run test:rate-limiter (if script is added to package.json)
 */

'use strict';

const path = require('path');
const Mocha = require('mocha');

// Load environment variables
require('dotenv').config();

// Ensure Redis URL is available
if (!process.env.REDIS_URL) {
    console.error('REDIS_URL environment variable is required');
    console.error('Please set REDIS_URL=redis://localhost:6379 or your Redis connection string');
    process.exit(1);
}

console.log('Running Rate Limiter Service tests in isolation...');
console.log('Redis URL:', process.env.REDIS_URL);
console.log('');

// Create Mocha instance
const mocha = new Mocha({
    timeout: 30000,
    colors: true,
    reporter: 'spec'
});

// Add the rate limiter test files
mocha.addFile(path.join(__dirname, 'services-tests', 'RateLimiterService.focused.spec.js'));
mocha.addFile(path.join(__dirname, 'services-tests', 'RateLimiterService.integration.spec.js'));
// Uncomment the line below to run the comprehensive test suite (may have timeout issues)
// mocha.addFile(path.join(__dirname, 'services-tests', 'RateLimiterService.spec.js'));

// Run the tests
mocha.run((failures) => {
    if (failures) {
        console.error(`\n${failures} test(s) failed`);
        process.exit(1);
    } else {
        console.log('\nAll Rate Limiter tests passed!');
        process.exit(0);
    }
});
