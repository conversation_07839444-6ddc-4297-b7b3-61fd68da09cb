'use strict';

const sinon = require('sinon');
const chai = require('chai');
const expect = chai.expect;

// Mock sails global for isolated testing
const mockSails = {
    config: {
        connections: {
            redis: process.env.REDIS_URL || 'redis://itrdev.lan:6379'
        },
        apiService: {
            apiKey: 'test-api-key-123',
            rateLimit: {
                read: {
                    points: 100,
                    duration: 60,
                    blockDuration: 300
                },
                write: {
                    points: 50,
                    duration: 60,
                    blockDuration: 600
                }
            }
        }
    },
    once: sinon.stub(),
    services: {}
};

// Mock loggers global for isolated testing
const mockLoggers = {
    debug_log: {
        verbose: sinon.stub(),
        warn: sinon.stub()
    },
    errors_log: {
        error: sinon.stub()
    }
};

// Set up globals before requiring the service
global.sails = mockSails;
global.loggers = mockLoggers;

const RateLimiterService = require('../../api/services/RateLimiterService');

// Make RateLimiterService available globally for policies
global.RateLimiterService = RateLimiterService;

describe('RateLimiterService - Integration Tests', function () {
    beforeEach(function () {
        // Reset all stubs
        Object.values(mockLoggers.debug_log).forEach(stub => stub.resetHistory());
        Object.values(mockLoggers.errors_log).forEach(stub => stub.resetHistory());
        mockSails.once.resetHistory();

        // Ensure global references are properly set
        global.loggers = mockLoggers;
        global.sails = mockSails;

        // Reset RateLimiterService state to ensure clean state between tests
        RateLimiterService._client = null;
        RateLimiterService._initPromise = null;
    });

    describe('Generic Policy Integration', function () {
        // Create mock policies that simulate typical rate limiting patterns
        const createMockReadPolicy = () => {
            const keySelector = (req) => req.user && req.user.clientId;

            function mockPolicy(req, res, next) {
                if (!mockPolicy.rateLimiter) {
                    mockPolicy.rateLimiter = RateLimiterService.createRateLimiter(
                        'api-service:read',
                        mockSails.config.apiService.rateLimit.read,
                        keySelector
                    );
                }
                return mockPolicy.rateLimiter(req, res, next);
            }

            return mockPolicy;
        };

        const createMockWritePolicy = () => {
            const keySelector = (req) => req.user && req.user.clientId;

            function mockPolicy(req, res, next) {
                if (!mockPolicy.rateLimiter) {
                    mockPolicy.rateLimiter = RateLimiterService.createRateLimiter(
                        'api-service:write',
                        mockSails.config.apiService.rateLimit.write,
                        keySelector
                    );
                }
                return mockPolicy.rateLimiter(req, res, next);
            }

            return mockPolicy;
        };

        it('should create rate limiter with correct configuration for read policy', function () {
            const readPolicy = createMockReadPolicy();

            const mockReq = {
                method: 'GET',
                path: '/api/service/v1/data/123',
                user: { clientId: 'api-client-read' }
            };
            const mockRes = {
                set: sinon.stub(),
                status: sinon.stub().returnsThis(),
                json: sinon.stub()
            };
            const mockNext = sinon.stub();

            // Test that the policy creates a rate limiter
            expect(typeof readPolicy).to.equal('function');

            // The policy should create a rate limiter middleware
            const result = readPolicy(mockReq, mockRes, mockNext);
            expect(result).to.be.a('promise');
        });

        it('should create rate limiter with correct configuration for write policy', function () {
            const writePolicy = createMockWritePolicy();

            const mockReq = {
                method: 'POST',
                path: '/api/service/v1/data/123/update',
                user: { clientId: 'api-client-write' }
            };
            const mockRes = {
                set: sinon.stub(),
                status: sinon.stub().returnsThis(),
                json: sinon.stub()
            };
            const mockNext = sinon.stub();

            // Test that the policy creates a rate limiter
            expect(typeof writePolicy).to.equal('function');

            // The policy should create a rate limiter middleware
            const result = writePolicy(mockReq, mockRes, mockNext);
            expect(result).to.be.a('promise');
        });

        it('should use the same rate limiter instance for multiple calls to read policy', function () {
            const readPolicy = createMockReadPolicy();

            const mockReq = {
                method: 'GET',
                path: '/api/service/v1/data/123',
                user: { clientId: 'api-client-cached' }
            };
            const mockRes = {
                set: sinon.stub(),
                status: sinon.stub().returnsThis(),
                json: sinon.stub()
            };
            const mockNext = sinon.stub();

            // First call should create the rate limiter
            const firstCall = readPolicy(mockReq, mockRes, mockNext);
            expect(firstCall).to.be.a('promise');

            // Second call should reuse the same rate limiter
            const secondCall = readPolicy(mockReq, mockRes, mockNext);
            expect(secondCall).to.be.a('promise');

            // The rate limiter should be cached in the policy function
            expect(readPolicy.rateLimiter).to.exist;
            expect(typeof readPolicy.rateLimiter).to.equal('function');
        });

        it('should work with different client identification patterns', function () {
            // Test IP-based key selector
            const ipBasedPolicy = () => {
                const keySelector = (req) => req.ip || req.connection.remoteAddress;
                return RateLimiterService.createRateLimiter(
                    'api-service:ip-based',
                    { points: 10, duration: 60 },
                    keySelector
                );
            };

            const middleware = ipBasedPolicy();
            const mockReq = {
                method: 'GET',
                path: '/api/service/v1/data',
                ip: '*************'
            };
            const mockRes = {
                set: sinon.stub(),
                status: sinon.stub().returnsThis(),
                json: sinon.stub()
            };
            const mockNext = sinon.stub();

            expect(typeof middleware).to.equal('function');
        });

        it('should work with user-based key selector patterns', function () {
            // Test user ID-based key selector
            const userBasedPolicy = () => {
                const keySelector = (req) => req.user && req.user.id;
                return RateLimiterService.createRateLimiter(
                    'api-service:user-based',
                    { points: 20, duration: 60 },
                    keySelector
                );
            };

            const middleware = userBasedPolicy();
            const mockReq = {
                method: 'POST',
                path: '/api/service/v1/data',
                user: { id: 'user-123' }
            };
            const mockRes = {
                set: sinon.stub(),
                status: sinon.stub().returnsThis(),
                json: sinon.stub()
            };
            const mockNext = sinon.stub();

            expect(typeof middleware).to.equal('function');
        });

        it('should work with session-based key selector patterns', function () {
            // Test session-based key selector
            const sessionBasedPolicy = () => {
                const keySelector = (req) => req.session && req.session.id;
                return RateLimiterService.createRateLimiter(
                    'api-service:session-based',
                    { points: 15, duration: 60 },
                    keySelector
                );
            };

            const middleware = sessionBasedPolicy();
            const mockReq = {
                method: 'GET',
                path: '/api/service/v1/data',
                session: { id: 'session-abc123' }
            };
            const mockRes = {
                set: sinon.stub(),
                status: sinon.stub().returnsThis(),
                json: sinon.stub()
            };
            const mockNext = sinon.stub();

            expect(typeof middleware).to.equal('function');
        });
    });

    describe('Configuration Integration', function () {
        it('should use configuration from sails.config.apiService', function () {
            // Verify the configuration is properly structured
            expect(mockSails.config.apiService).to.be.an('object');
            expect(mockSails.config.apiService.rateLimit).to.be.an('object');
            expect(mockSails.config.apiService.rateLimit.read).to.be.an('object');
            expect(mockSails.config.apiService.rateLimit.read.points).to.be.a('number');
            expect(mockSails.config.apiService.rateLimit.read.duration).to.be.a('number');
            expect(mockSails.config.apiService.rateLimit.write).to.be.an('object');
            expect(mockSails.config.apiService.rateLimit.write.points).to.be.a('number');
            expect(mockSails.config.apiService.rateLimit.write.duration).to.be.a('number');
        });

        it('should handle missing configuration gracefully', function () {
            // Temporarily remove configuration
            const originalConfig = mockSails.config.apiService;
            delete mockSails.config.apiService;

            try {
                // This should not crash even without configuration
                const middleware = RateLimiterService.createRateLimiter(
                    'test-no-config',
                    { points: 10, duration: 60 },
                    (req) => req.user && req.user.clientId
                );
                expect(typeof middleware).to.equal('function');
            } finally {
                // Restore configuration
                mockSails.config.apiService = originalConfig;
            }
        });

        it('should support different rate limit configurations for different operations', function () {
            // Test that different configurations can be used
            const readConfig = mockSails.config.apiService.rateLimit.read;
            const writeConfig = mockSails.config.apiService.rateLimit.write;

            expect(readConfig.points).to.not.equal(writeConfig.points);
            expect(readConfig.blockDuration).to.not.equal(writeConfig.blockDuration);

            // Both should be valid configurations
            const readMiddleware = RateLimiterService.createRateLimiter(
                'test-read-config',
                readConfig,
                (req) => req.user && req.user.clientId
            );
            const writeMiddleware = RateLimiterService.createRateLimiter(
                'test-write-config',
                writeConfig,
                (req) => req.user && req.user.clientId
            );

            expect(typeof readMiddleware).to.equal('function');
            expect(typeof writeMiddleware).to.equal('function');
        });
    });

    describe('Error Response Format', function () {
        it('should return proper error format when rate limited', async function () {
            const config = { points: 1, duration: 60 };
            const keySelector = (req) => req.user.clientId;
            
            // Mock a rate limiter that always throws rate limit error
            const originalCreateRateLimiter = RateLimiterService._createRateLimiter;
            RateLimiterService._createRateLimiter = function() {
                return {
                    consume: () => {
                        const error = new Error('Rate limit exceeded');
                        error.remainingPoints = 0;
                        error.msBeforeNext = 30000;
                        return Promise.reject(error);
                    }
                };
            };

            const middleware = RateLimiterService.createRateLimiter('test-error-format', config, keySelector);
            
            const mockReq = {
                method: 'GET',
                path: '/api/service/v1/data',
                user: { clientId: 'api-client-test' }
            };
            const mockRes = {
                set: sinon.stub(),
                status: sinon.stub().returnsThis(),
                json: sinon.stub()
            };
            const mockNext = sinon.stub();

            // Initialize client
            RateLimiterService._client = { status: 'ready' };

            await middleware(mockReq, mockRes, mockNext);

            // Should return 429 status
            expect(mockRes.status.calledWith(429)).to.be.true;
            
            // Should return proper error format
            expect(mockRes.json.calledOnce).to.be.true;
            const errorResponse = mockRes.json.firstCall.args[0];
            expect(errorResponse).to.have.property('error', 'rate_limited');
            expect(errorResponse).to.have.property('rateLimiter', 'test-error-format');
            expect(errorResponse).to.have.property('retryAfterMs');

            // Restore
            RateLimiterService._createRateLimiter = originalCreateRateLimiter;
        });
    });
});
