'use strict';

const sinon = require('sinon');
const chai = require('chai');
const expect = chai.expect;

// Minimal globals for isolated testing (created only if not present)
const mockSails = {
    config: {
        connections: {
            redis: process.env.REDIS_URL
        },
        apiService: {
            apiKey: 'test-api-key-123',
            rateLimit: {
                read: { points: 100, duration: 60, blockDuration: 300 },
                write: { points: 50, duration: 60, blockDuration: 600 }
            }
        }
    },
    once: sinon.stub(),
    services: {}
};

const mockLoggers = {
    debug_log: { verbose: sinon.stub(), warn: sinon.stub() },
    errors_log: { error: sinon.stub() }
};

// Only set up mocks if globals don't exist (isolated mode)
const isIsolatedMode = !global.sails;
if (isIsolatedMode) {
    global.sails = mockSails;
    global.loggers = mockLoggers;
}

const RateLimiterService = require('../../api/services/RateLimiterService');
global.RateLimiterService = RateLimiterService;

describe('RateLimiterService - Integration Tests', function () {
    let sandbox;

    function makeReqResNext(overrides) {
        overrides = overrides || {};
        const req = Object.assign(
            { method: 'GET', path: '/api/service/v1/data', user: { clientId: 'api-client' } },
            overrides.req || {}
        );
        const res = Object.assign(
            { set: sinon.stub(), status: sinon.stub().returnsThis(), json: sinon.stub() },
            overrides.res || {}
        );
        const next = sinon.stub();
        return { req, res, next };
    }

    beforeEach(function () {
        sandbox = sinon.createSandbox();

        // Reset logger stubs (only in isolated mode)
        if (isIsolatedMode) {
            for (const k in mockLoggers.debug_log) {
                if (mockLoggers.debug_log[k] && mockLoggers.debug_log[k].resetHistory) {
                    mockLoggers.debug_log[k].resetHistory();
                }
            }
            for (const k in mockLoggers.errors_log) {
                if (mockLoggers.errors_log[k] && mockLoggers.errors_log[k].resetHistory) {
                    mockLoggers.errors_log[k].resetHistory();
                }
            }
            if (mockSails.once.resetHistory) mockSails.once.resetHistory();
        }

        // Reset service state
        RateLimiterService._client = null;
        RateLimiterService._initPromise = null;
    });

    afterEach(function () {
        sandbox.restore();
        RateLimiterService._client = null;
        RateLimiterService._initPromise = null;
    });

    describe('Generic Policy Integration', function () {
        function createMockReadPolicy() {
            const keySelector = function (req) {
                return req && req.user ? req.user.clientId : undefined;
            };
            function mockPolicy(req, res, next) {
                if (!mockPolicy.rateLimiter) {
                    mockPolicy.rateLimiter = RateLimiterService.createRateLimiter(
                        'api-service:read',
                        mockSails.config.apiService.rateLimit.read,
                        keySelector
                    );
                }
                return mockPolicy.rateLimiter(req, res, next);
            }
            return mockPolicy;
        }

        function createMockWritePolicy() {
            const keySelector = function (req) {
                return req && req.user ? req.user.clientId : undefined;
            };
            function mockPolicy(req, res, next) {
                if (!mockPolicy.rateLimiter) {
                    mockPolicy.rateLimiter = RateLimiterService.createRateLimiter(
                        'api-service:write',
                        mockSails.config.apiService.rateLimit.write,
                        keySelector
                    );
                }
                return mockPolicy.rateLimiter(req, res, next);
            }
            return mockPolicy;
        }

        it('should create rate limiter with correct configuration for read policy', function () {
            const readPolicy = createMockReadPolicy();
            const ctx = makeReqResNext({
                req: { method: 'GET', path: '/api/service/v1/data/123', user: { clientId: 'api-client-read' } }
            });

            expect(typeof readPolicy).to.equal('function');

            const result = readPolicy(ctx.req, ctx.res, ctx.next);
            const isThenable = !!(result && typeof result.then === 'function');
            expect(isThenable).to.equal(true);
        });

        it('should create rate limiter with correct configuration for write policy', function () {
            const writePolicy = createMockWritePolicy();
            const ctx = makeReqResNext({
                req: { method: 'POST', path: '/api/service/v1/data/123/update', user: { clientId: 'api-client-write' } }
            });

            expect(typeof writePolicy).to.equal('function');

            const result = writePolicy(ctx.req, ctx.res, ctx.next);
            const isThenable = !!(result && typeof result.then === 'function');
            expect(isThenable).to.equal(true);
        });

        it('should use the same rate limiter instance for multiple calls to read policy', function () {
            const readPolicy = createMockReadPolicy();
            const ctx = makeReqResNext({
                req: { method: 'GET', path: '/api/service/v1/data/123', user: { clientId: 'api-client-cached' } }
            });

            const firstCall = readPolicy(ctx.req, ctx.res, ctx.next);
            const secondCall = readPolicy(ctx.req, ctx.res, ctx.next);

            const isThenable1 = !!(firstCall && typeof firstCall.then === 'function');
            const isThenable2 = !!(secondCall && typeof secondCall.then === 'function');

            expect(isThenable1).to.equal(true);
            expect(isThenable2).to.equal(true);
            expect(readPolicy.rateLimiter).to.exist;
            expect(typeof readPolicy.rateLimiter).to.equal('function');
        });

        it('should work with different client identification patterns', function () {
            function ipBasedPolicy() {
                const keySelector = function (req) {
                    return (req && req.ip) ? req.ip : (req && req.connection ? req.connection.remoteAddress : undefined);
                };
                return RateLimiterService.createRateLimiter('api-service:ip-based', { points: 10, duration: 60 }, keySelector);
            }

            const middleware = ipBasedPolicy();
            const ctx = makeReqResNext({ req: { ip: '*************' } });

            expect(typeof middleware).to.equal('function');
            // We do not execute the middleware here to keep this deterministic.
        });

        it('should work with user-based key selector patterns', function () {
            function userBasedPolicy() {
                const keySelector = function (req) {
                    return req && req.user ? req.user.id : undefined;
                };
                return RateLimiterService.createRateLimiter('api-service:user-based', { points: 20, duration: 60 }, keySelector);
            }

            const middleware = userBasedPolicy();
            const ctx = makeReqResNext({
                req: { method: 'POST', user: { id: 'user-123' } }
            });

            expect(typeof middleware).to.equal('function');
        });

        it('should work with session-based key selector patterns', function () {
            function sessionBasedPolicy() {
                const keySelector = function (req) {
                    return req && req.session ? req.session.id : undefined;
                };
                return RateLimiterService.createRateLimiter('api-service:session-based', { points: 15, duration: 60 }, keySelector);
            }

            const middleware = sessionBasedPolicy();
            const ctx = makeReqResNext({
                req: { session: { id: 'session-abc123' } }
            });

            expect(typeof middleware).to.equal('function');
        });
    });

    describe('Configuration Integration', function () {
        it('should use configuration from sails.config.apiService', function () {
            expect(mockSails.config.apiService).to.be.an('object');
            expect(mockSails.config.apiService.rateLimit).to.be.an('object');
            expect(mockSails.config.apiService.rateLimit.read).to.be.an('object');
            expect(mockSails.config.apiService.rateLimit.read.points).to.be.a('number');
            expect(mockSails.config.apiService.rateLimit.read.duration).to.be.a('number');
            expect(mockSails.config.apiService.rateLimit.write).to.be.an('object');
            expect(mockSails.config.apiService.rateLimit.write.points).to.be.a('number');
            expect(mockSails.config.apiService.rateLimit.write.duration).to.be.a('number');
        });

        it('should handle missing configuration gracefully', function () {
            const originalConfig = mockSails.config.apiService;
            delete mockSails.config.apiService;

            try {
                const middleware = RateLimiterService.createRateLimiter(
                    'test-no-config',
                    { points: 10, duration: 60 },
                    function (req) {
                        return req && req.user ? req.user.clientId : undefined;
                    }
                );
                expect(typeof middleware).to.equal('function');
            } finally {
                mockSails.config.apiService = originalConfig;
            }
        });

        it('should support different rate limit configurations for different operations', function () {
            const readConfig = mockSails.config.apiService.rateLimit.read;
            const writeConfig = mockSails.config.apiService.rateLimit.write;

            expect(readConfig.points).to.not.equal(writeConfig.points);
            expect(readConfig.blockDuration).to.not.equal(writeConfig.blockDuration);

            const readMiddleware = RateLimiterService.createRateLimiter(
                'test-read-config',
                readConfig,
                function (req) {
                    return req && req.user ? req.user.clientId : undefined;
                }
            );
            const writeMiddleware = RateLimiterService.createRateLimiter(
                'test-write-config',
                writeConfig,
                function (req) {
                    return req && req.user ? req.user.clientId : undefined;
                }
            );

            expect(typeof readMiddleware).to.equal('function');
            expect(typeof writeMiddleware).to.equal('function');
        });
    });

    describe('Error Response Format', function () {
        it('should return proper error format when rate limited', async function () {
            const config = { points: 1, duration: 60 };
            const keySelector = function (req) { return req.user.clientId; };

            const originalCreateRateLimiter = RateLimiterService._createRateLimiter;
            try {
                RateLimiterService._createRateLimiter = function () {
                    return {
                        consume: function () {
                            const error = new Error('Rate limit exceeded');
                            error.remainingPoints = 0;
                            error.msBeforeNext = 30000;
                            return Promise.reject(error);
                        }
                    };
                };

                const middleware = RateLimiterService.createRateLimiter('test-error-format', config, keySelector);

                const ctx = makeReqResNext({
                    req: { method: 'GET', path: '/api/service/v1/data', user: { clientId: 'api-client-test' } }
                });

                // Pretend client is ready so limiter path is taken
                RateLimiterService._client = { status: 'ready' };

                await middleware(ctx.req, ctx.res, ctx.next);

                expect(ctx.res.status.calledWith(429)).to.be.true;
                expect(ctx.res.json.calledOnce).to.be.true;

                const payload = ctx.res.json.firstCall.args[0];
                expect(payload).to.have.property('error', 'rate_limited');
                expect(payload).to.have.property('rateLimiter', 'test-error-format');
                expect(payload).to.have.property('retryAfterMs');
                expect(ctx.next.called).to.be.false;
            } finally {
                RateLimiterService._createRateLimiter = originalCreateRateLimiter;
            }
        });
    });
});
