'use strict';

const sinon = require('sinon');
const chai = require('chai');
const expect = chai.expect;
const crypto = require('crypto');

// Minimal globals for isolated testing
const mockSails = {
    config: {
        volleyStation: {
            apiKey: 'test-volley-station-api-key-123',
            rateLimit: {
                read: {
                    points: 100,
                    duration: 60,
                    blockDuration: 300
                },
                write: {
                    points: 50,
                    duration: 60,
                    blockDuration: 600
                }
            }
        }
    },
    once: sinon.stub(),
    services: {}
};

const mockLoggers = {
    debug_log: { verbose: sinon.stub(), warn: sinon.stub() },
    errors_log: { error: sinon.stub() }
};

if (!global.sails) global.sails = mockSails;
if (!global.loggers) global.loggers = mockLoggers;

describe('Volley Station API Tests', function () {
    beforeEach(function () {
        for (const k in mockLoggers.debug_log) {
            if (mockLoggers.debug_log[k] && mockLoggers.debug_log[k].resetHistory) {
                mockLoggers.debug_log[k].resetHistory();
            }
        }
        for (const k in mockLoggers.errors_log) {
            if (mockLoggers.errors_log[k] && mockLoggers.errors_log[k].resetHistory) {
                mockLoggers.errors_log[k].resetHistory();
            }
        }
        if (global.sails === mockSails && mockSails.once.resetHistory) {
            mockSails.once.resetHistory();
        }
    });

    describe('Authentication Policy', function () {
        let authPolicy;

        before(function () {
            authPolicy = require('../../api/policies/volley-station/auth');
        });

        it('should authenticate valid API key', function () {
            const mockReq = {
                get: sinon.stub().returns('test-volley-station-api-key-123'),
                user: {}
            };
            const mockRes = {
                status: sinon.stub().returnsThis(),
                json: sinon.stub()
            };
            const mockNext = sinon.stub();

            authPolicy(mockReq, mockRes, mockNext);

            expect(mockNext.calledOnce).to.be.true;
            expect(mockReq.user.clientId).to.be.a('string');
            expect(mockReq.user.clientId.length).to.equal(8);
        });

        it('should reject missing authorization token', function () {
            const mockReq = {
                get: sinon.stub().returns(undefined),
                user: {}
            };
            const mockRes = {
                status: sinon.stub().returnsThis(),
                json: sinon.stub()
            };
            const mockNext = sinon.stub();

            authPolicy(mockReq, mockRes, mockNext);

            expect(mockNext.called).to.be.false;
            expect(mockRes.status.calledWith(401)).to.be.true;
            expect(mockRes.json.calledOnce).to.be.true;
            const response = mockRes.json.firstCall.args[0];
            expect(response.message).to.include('Authorization token is missing');
        });

        it('should reject invalid API key', function () {
            const mockReq = {
                get: sinon.stub().returns('invalid-api-key'),
                user: {}
            };
            const mockRes = {
                status: sinon.stub().returnsThis(),
                json: sinon.stub()
            };
            const mockNext = sinon.stub();

            authPolicy(mockReq, mockRes, mockNext);

            expect(mockNext.called).to.be.false;
            expect(mockRes.status.calledWith(401)).to.be.true;
            expect(mockRes.json.calledOnce).to.be.true;
            const response = mockRes.json.firstCall.args[0];
            expect(response.message).to.include('Authorization token invalid');
        });

        it('should generate consistent client IDs for the same API key', function () {
            const apiKey = 'test-volley-station-api-key-123';

            const mockReq1 = { get: sinon.stub().returns(apiKey), user: {} };
            const mockReq2 = { get: sinon.stub().returns(apiKey), user: {} };
            const mockRes = { status: sinon.stub().returnsThis(), json: sinon.stub() };
            const mockNext = sinon.stub();

            authPolicy(mockReq1, mockRes, mockNext);
            authPolicy(mockReq2, mockRes, mockNext);

            expect(mockReq1.user.clientId).to.equal(mockReq2.user.clientId);
        });

        it('should generate different client IDs for different API keys', function () {
            const mockReq1 = { get: sinon.stub().returns('test-volley-station-api-key-123'), user: {} };
            const mockReq2 = { get: sinon.stub().returns('test-volley-station-api-key-456'), user: {} };
            const mockRes = { status: sinon.stub().returnsThis(), json: sinon.stub() };
            const mockNext = sinon.stub();

            authPolicy(mockReq1, mockRes, mockNext);
            authPolicy(mockReq2, mockRes, mockNext);

            expect(mockReq1.user.clientId).to.not.equal(mockReq2.user.clientId);
        });

        it('should use SHA256 hash for client ID generation', function () {
            const apiKey = 'test-volley-station-api-key-123';
            const expectedClientId = crypto
                .createHash('sha256')
                .update(apiKey)
                .digest('hex')
                .slice(0, 8);

            const mockReq = { get: sinon.stub().returns(apiKey), user: {} };
            const mockRes = { status: sinon.stub().returnsThis(), json: sinon.stub() };
            const mockNext = sinon.stub();

            authPolicy(mockReq, mockRes, mockNext);

            expect(mockReq.user.clientId).to.equal(expectedClientId);
        });

        it('should preserve existing user object properties', function () {
            const mockReq = {
                get: sinon.stub().returns('test-volley-station-api-key-123'),
                user: { existingProperty: 'should-be-preserved' }
            };
            const mockRes = { status: sinon.stub().returnsThis(), json: sinon.stub() };
            const mockNext = sinon.stub();

            authPolicy(mockReq, mockRes, mockNext);

            expect(mockNext.calledOnce).to.be.true;
            expect(mockReq.user.existingProperty).to.equal('should-be-preserved');
            expect(mockReq.user.clientId).to.be.a('string');
        });
    });

    describe('Rate Limiting Policies', function () {
        let rateLimitReadPolicy, rateLimitWritePolicy;

        before(function () {
            global.RateLimiterService = {
                createRateLimiter: sinon.stub().returns(sinon.stub().resolves())
            };

            rateLimitReadPolicy = require('../../api/policies/volley-station/rateLimitRead');
            rateLimitWritePolicy = require('../../api/policies/volley-station/rateLimitWrite');
        });

        beforeEach(function () {
            if (global.RateLimiterService && global.RateLimiterService.createRateLimiter.resetHistory) {
                global.RateLimiterService.createRateLimiter.resetHistory();
            }
            delete rateLimitReadPolicy.rateLimiter;
            delete rateLimitWritePolicy.rateLimiter;
        });

        it('should create read rate limiter with correct configuration', function () {
            const mockReq = {
                method: 'GET',
                path: '/api/volley-station/v1/events/123/schedule',
                user: { clientId: 'vs-client-read' }
            };
            const mockRes = { set: sinon.stub(), status: sinon.stub().returnsThis(), json: sinon.stub() };
            const mockNext = sinon.stub();

            rateLimitReadPolicy(mockReq, mockRes, mockNext);

            expect(global.RateLimiterService.createRateLimiter.calledOnce).to.be.true;
            const args = global.RateLimiterService.createRateLimiter.firstCall.args;
            const name = args[0];
            const config = args[1];
            const keySelector = args[2];

            expect(name).to.equal('volley-station:read');
            expect(config).to.deep.equal(mockSails.config.volleyStation.rateLimit.read);
            expect(typeof keySelector).to.equal('function');
            expect(keySelector(mockReq)).to.equal('vs-client-read');
        });

        it('should create write rate limiter with correct configuration', function () {
            const mockReq = {
                method: 'POST',
                path: '/api/volley-station/v1/events/123/update',
                user: { clientId: 'vs-client-write' }
            };
            const mockRes = { set: sinon.stub(), status: sinon.stub().returnsThis(), json: sinon.stub() };
            const mockNext = sinon.stub();

            rateLimitWritePolicy(mockReq, mockRes, mockNext);

            expect(global.RateLimiterService.createRateLimiter.calledOnce).to.be.true;
            const args = global.RateLimiterService.createRateLimiter.firstCall.args;
            const name = args[0];
            const config = args[1];
            const keySelector = args[2];

            expect(name).to.equal('volley-station:write');
            // Current implementation uses the read config for write policy
            expect(config).to.deep.equal(mockSails.config.volleyStation.rateLimit.read);
            expect(typeof keySelector).to.equal('function');
            expect(keySelector(mockReq)).to.equal('vs-client-write');
        });

        it('should cache rate limiter instances', function () {
            const mockReq = {
                method: 'GET',
                path: '/api/volley-station/v1/events/123/schedule',
                user: { clientId: 'vs-client-cached' }
            };
            const mockRes = { set: sinon.stub(), status: sinon.stub().returnsThis(), json: sinon.stub() };
            const mockNext = sinon.stub();

            rateLimitReadPolicy(mockReq, mockRes, mockNext);
            expect(global.RateLimiterService.createRateLimiter.calledOnce).to.be.true;

            rateLimitReadPolicy(mockReq, mockRes, mockNext);
            expect(global.RateLimiterService.createRateLimiter.calledOnce).to.be.true;

            expect(!!rateLimitReadPolicy.rateLimiter).to.be.true;
        });

        it('should handle requests without user object', function () {
            const mockReq = {
                method: 'GET',
                path: '/api/volley-station/v1/events/123/schedule'
            };
            const mockRes = { set: sinon.stub(), status: sinon.stub().returnsThis(), json: sinon.stub() };
            const mockNext = sinon.stub();

            rateLimitReadPolicy(mockReq, mockRes, mockNext);

            expect(global.RateLimiterService.createRateLimiter.calledOnce).to.be.true;
            const keySelector = global.RateLimiterService.createRateLimiter.firstCall.args[2];
            expect(keySelector(mockReq)).to.equal(undefined);
        });
    });

    describe('Event Access Policy', function () {
        let eventAccessPolicy;

        before(function () {
            global.VolleyStationService = {
                isEnabledForEvent: sinon.stub()
            };

            eventAccessPolicy = require('../../api/policies/volley-station/eventAccess');
        });

        beforeEach(function () {
            if (global.VolleyStationService.isEnabledForEvent.resetHistory) {
                global.VolleyStationService.isEnabledForEvent.resetHistory();
            }
        });

        it('should allow access for enabled events', async function () {
            global.VolleyStationService.isEnabledForEvent.resolves(true);

            const mockReq = { params: { eventId: '123' } };
            const mockRes = { validation: sinon.stub(), status: sinon.stub().returnsThis(), json: sinon.stub() };
            const mockNext = sinon.stub();

            await eventAccessPolicy(mockReq, mockRes, mockNext);

            expect(global.VolleyStationService.isEnabledForEvent.calledWith(123)).to.be.true;
            expect(mockNext.calledOnce).to.be.true;
            expect(mockRes.status.called).to.be.false;
        });

        it('should deny access for disabled events', async function () {
            global.VolleyStationService.isEnabledForEvent.resolves(false);

            const mockReq = { params: { eventId: '456' } };
            const mockRes = { validation: sinon.stub(), status: sinon.stub().returnsThis(), json: sinon.stub() };
            const mockNext = sinon.stub();

            await eventAccessPolicy(mockReq, mockRes, mockNext);

            expect(global.VolleyStationService.isEnabledForEvent.calledWith(456)).to.be.true;
            expect(mockNext.called).to.be.false;
            expect(mockRes.status.calledWith(403)).to.be.true;
            expect(mockRes.json.calledOnce).to.be.true;

            const response = mockRes.json.firstCall.args[0];
            expect(response.message).to.include('Event not available for VolleyStation integration');
        });

        it('should reject invalid event IDs', async function () {
            const mockReq = { params: { eventId: 'invalid' } };
            const mockRes = { validation: sinon.stub(), status: sinon.stub().returnsThis(), json: sinon.stub() };
            const mockNext = sinon.stub();

            await eventAccessPolicy(mockReq, mockRes, mockNext);

            expect(global.VolleyStationService.isEnabledForEvent.called).to.be.false;
            expect(mockNext.called).to.be.false;
            expect(mockRes.validation.calledWith('Invalid event identifier passed')).to.be.true;
        });

        it('should handle missing event ID', async function () {
            const mockReq = { params: {} };
            const mockRes = { validation: sinon.stub(), status: sinon.stub().returnsThis(), json: sinon.stub() };
            const mockNext = sinon.stub();

            await eventAccessPolicy(mockReq, mockRes, mockNext);

            expect(global.VolleyStationService.isEnabledForEvent.called).to.be.false;
            expect(mockNext.called).to.be.false;
            expect(mockRes.validation.calledWith('Invalid event identifier passed')).to.be.true;
        });

        it('should handle service errors gracefully', async function () {
            global.VolleyStationService.isEnabledForEvent.rejects(new Error('Database error'));

            const mockReq = { params: { eventId: '789' } };
            const mockRes = { validation: sinon.stub(), status: sinon.stub().returnsThis(), json: sinon.stub() };
            const mockNext = sinon.stub();

            await eventAccessPolicy(mockReq, mockRes, mockNext);

            expect(global.VolleyStationService.isEnabledForEvent.calledWith(789)).to.be.true;
            expect(mockNext.called).to.be.false;
            expect(mockRes.status.calledWith(500)).to.be.true;
            expect(mockRes.json.calledOnce).to.be.true;

            const response = mockRes.json.firstCall.args[0];
            expect(response.message).to.include('Unable to verify event access');
            expect(mockLoggers.errors_log.error.called).to.be.true;
        });
    });

    describe('Configuration', function () {
        it('should have proper volley station configuration structure', function () {
            expect(mockSails.config.volleyStation).to.be.an('object');
            expect(mockSails.config.volleyStation.apiKey).to.be.a('string');
            expect(mockSails.config.volleyStation.rateLimit).to.be.an('object');
            expect(mockSails.config.volleyStation.rateLimit.read).to.be.an('object');
            expect(mockSails.config.volleyStation.rateLimit.write).to.be.an('object');
        });

        it('should have rate limit configurations defined', function () {
            const readConfig = mockSails.config.volleyStation.rateLimit.read;
            const writeConfig = mockSails.config.volleyStation.rateLimit.write;

            expect(readConfig).to.be.an('object');
            expect(writeConfig).to.be.an('object');

            expect(readConfig.points).to.be.a('number');
            expect(readConfig.duration).to.be.a('number');
            expect(readConfig.blockDuration).to.be.a('number');

            expect(writeConfig.points).to.be.a('number');
            expect(writeConfig.duration).to.be.a('number');
            expect(writeConfig.blockDuration).to.be.a('number');
        });
    });
});
