'use strict';

const Redis = require('ioredis');
const sinon = require('sinon');
const chai = require('chai');
const chaiAsPromised = require('chai-as-promised');

chai.use(chaiAsPromised);
const expect = chai.expect;

// Mock sails global for isolated testing
const mockSails = {
    config: {
        connections: {
            redis: process.env.REDIS_URL || 'redis://itrdev.lan:6379'
        }
    },
    once: sinon.stub(),
    services: {}
};

// Mock loggers global for isolated testing
const mockLoggers = {
    debug_log: {
        verbose: sinon.stub(),
        warn: sinon.stub()
    },
    errors_log: {
        error: sinon.stub()
    }
};

// Set up globals before requiring the service
global.sails = mockSails;
global.loggers = mockLoggers;

const RateLimiterService = require('../../api/services/RateLimiterService');

describe('RateLimiterService', function () {
    let redisClient;
    let originalClient;

    before(async function () {
        // Create a real Redis client for testing
        redisClient = new Redis(process.env.REDIS_URL || 'redis://itrdev.lan:6379', {
            keyPrefix: 'test:rate-limit:',
            lazyConnect: true
        });

        try {
            await redisClient.connect();
            console.log('Connected to Redis for testing');
        } catch (err) {
            console.warn('Redis not available, some tests will be skipped:', err.message);
        }
    });

    after(async function () {
        if (redisClient && redisClient.status === 'ready') {
            // Clean up test keys
            const keys = await redisClient.keys('test:rate-limit:*');
            if (keys.length > 0) {
                await redisClient.del(...keys);
            }
            await redisClient.quit();
        }
    });

    beforeEach(function () {
        // Reset all stubs
        Object.values(mockLoggers.debug_log).forEach(stub => stub.resetHistory());
        Object.values(mockLoggers.errors_log).forEach(stub => stub.resetHistory());
        mockSails.once.resetHistory();

        // Reset the service state
        originalClient = RateLimiterService._client;
        RateLimiterService._client = null;
        RateLimiterService._initPromise = null;
    });

    afterEach(function () {
        // Restore original state
        RateLimiterService._client = originalClient;
    });

    describe('_initClient()', function () {
        it('should initialize Redis client successfully', async function () {
            if (!redisClient || redisClient.status !== 'ready') {
                this.skip();
            }

            const result = await RateLimiterService._initClient();
            expect(result).to.be.true;
            expect(RateLimiterService._client).to.not.be.null;
            expect(mockLoggers.debug_log.verbose.calledWith('RateLimiterService Redis ready')).to.be.true;
        });

        it('should handle Redis connection failure gracefully', async function () {
            // Mock a bad Redis URL
            mockSails.config.connections.redis = 'redis://invalid-host:6379';

            const result = await RateLimiterService._initClient();
            expect(result).to.be.false;
            expect(RateLimiterService._client).to.be.null;
            expect(mockLoggers.errors_log.error.calledWith('RateLimiterService init failed:')).to.be.true;

            // Restore good URL
            mockSails.config.connections.redis = process.env.REDIS_URL || 'redis://itrdev.lan:6379';
        });

        it('should return existing promise if already initializing', async function () {
            if (!redisClient || redisClient.status !== 'ready') {
                this.skip();
            }

            const promise1 = RateLimiterService._initClient();
            const promise2 = RateLimiterService._initClient();
            
            expect(promise1).to.equal(promise2);
            
            const [result1, result2] = await Promise.all([promise1, promise2]);
            expect(result1).to.be.true;
            expect(result2).to.be.true;
        });
    });

    describe('_softTimeout()', function () {
        it('should resolve with promise result if completed within timeout', async function () {
            const testPromise = Promise.resolve('success');
            const result = await RateLimiterService._softTimeout(testPromise, 1000);
            expect(result).to.equal('success');
        });

        it('should resolve with timeout flag if promise takes too long', async function () {
            const slowPromise = new Promise(resolve => setTimeout(() => resolve('slow'), 100));
            const result = await RateLimiterService._softTimeout(slowPromise, 50);
            expect(result).to.equal('__RL_TIMEOUT__');
        });

        it('should clear timeout when promise resolves first', async function () {
            const fastPromise = Promise.resolve('fast');
            const clearTimeoutSpy = sinon.spy(global, 'clearTimeout');
            
            await RateLimiterService._softTimeout(fastPromise, 1000);
            
            expect(clearTimeoutSpy.called).to.be.true;
            clearTimeoutSpy.restore();
        });
    });

    describe('_getKeySelectorSafe()', function () {
        it('should return key selector result on success', async function () {
            const mockReq = { user: { id: 'test-user' } };
            const keySelector = async (req) => req.user.id;
            
            const result = await RateLimiterService._getKeySelectorSafe(keySelector, mockReq);
            expect(result).to.equal('test-user');
        });

        it('should return null and log error on key selector failure', async function () {
            const mockReq = {};
            const keySelector = async () => { throw new Error('Key selector error'); };
            
            const result = await RateLimiterService._getKeySelectorSafe(keySelector, mockReq);
            expect(result).to.be.null;
            expect(mockLoggers.errors_log.error.calledWith('RateLimiterService key selector error:')).to.be.true;
        });
    });

    describe('createRateLimiter()', function () {
        let mockReq, mockRes, mockNext;

        beforeEach(function () {
            mockReq = {
                method: 'GET',
                path: '/test',
                user: { clientId: 'test-client' }
            };
            mockRes = {
                set: sinon.stub(),
                status: sinon.stub().returnsThis(),
                json: sinon.stub()
            };
            mockNext = sinon.stub();
        });

        it('should skip OPTIONS requests', async function () {
            const config = { points: 10, duration: 60 };
            const keySelector = (req) => req.user.clientId;
            const middleware = RateLimiterService.createRateLimiter('test', config, keySelector);

            mockReq.method = 'OPTIONS';
            await middleware(mockReq, mockRes, mockNext);

            expect(mockNext.calledOnce).to.be.true;
            expect(mockRes.set.called).to.be.false;
        });

        it('should skip when key selector returns null', async function () {
            const config = { points: 10, duration: 60 };
            const keySelector = () => null;
            const middleware = RateLimiterService.createRateLimiter('test', config, keySelector);

            await middleware(mockReq, mockRes, mockNext);

            expect(mockNext.calledOnce).to.be.true;
            expect(mockRes.set.called).to.be.false;
        });

        it('should proceed when Redis client is not ready (fail-open)', function (done) {
            const config = { points: 10, duration: 60 };
            const keySelector = (req) => req.user.clientId;

            // Store original state
            const originalClient = RateLimiterService._client;
            const originalPromise = RateLimiterService._initPromise;

            // Force client to be null to simulate no connection
            RateLimiterService._client = null;
            RateLimiterService._initPromise = null;

            const middleware = RateLimiterService.createRateLimiter('test-fail-open', config, keySelector);

            middleware(mockReq, mockRes, mockNext).then(() => {
                try {
                    expect(mockNext.calledOnce).to.be.true;
                    expect(mockRes.set.called).to.be.false;

                    // Restore original state
                    RateLimiterService._client = originalClient;
                    RateLimiterService._initPromise = originalPromise;
                    done();
                } catch (err) {
                    // Restore original state
                    RateLimiterService._client = originalClient;
                    RateLimiterService._initPromise = originalPromise;
                    done(err);
                }
            }).catch(done);
        });

        it('should set rate limit headers on successful request', async function () {
            if (!redisClient || redisClient.status !== 'ready') {
                this.skip();
            }

            const config = { points: 5, duration: 60 };
            const keySelector = (req) => req.user.clientId;

            // Ensure clean state and initialize
            RateLimiterService._client = null;
            RateLimiterService._initPromise = null;
            await RateLimiterService._initClient();

            const middleware = RateLimiterService.createRateLimiter('test-headers-unique', config, keySelector);
            await middleware(mockReq, mockRes, mockNext);

            expect(mockNext.calledOnce).to.be.true;

            // Debug: log what was called
            if (!mockRes.set.called) {
                console.log('mockRes.set was not called');
                console.log('Client status:', RateLimiterService._client ? RateLimiterService._client.status : 'null');
            }

            expect(mockRes.set.called).to.be.true;

            if (mockRes.set.called) {
                const headers = mockRes.set.firstCall.args[0];
                expect(headers).to.have.property('RateLimit-Limit', '5');
                expect(headers).to.have.property('RateLimit-Remaining');
                expect(headers).to.have.property('RateLimit-Reset');
            }
        });

        it('should return 429 when rate limit exceeded', async function () {
            if (!redisClient || redisClient.status !== 'ready') {
                this.skip();
            }

            const config = { points: 1, duration: 60 };
            const keySelector = (req) => req.user.clientId;
            const middleware = RateLimiterService.createRateLimiter('test-exceed', config, keySelector);

            // Initialize the service first
            await RateLimiterService._initClient();

            // First request should succeed
            await middleware(mockReq, mockRes, mockNext);
            expect(mockNext.calledOnce).to.be.true;

            // Reset mocks for second request
            mockNext.resetHistory();
            mockRes.set.resetHistory();
            mockRes.status.resetHistory();
            mockRes.json.resetHistory();

            // Second request should be rate limited
            await middleware(mockReq, mockRes, mockNext);

            expect(mockNext.called).to.be.false;
            expect(mockRes.status.calledWith(429)).to.be.true;
            expect(mockRes.json.calledOnce).to.be.true;

            const responseBody = mockRes.json.firstCall.args[0];
            expect(responseBody).to.have.property('error', 'rate_limited');
            expect(responseBody).to.have.property('rateLimiter', 'test-exceed');
        });

        it('should handle soft timeout gracefully', async function () {
            if (!redisClient || redisClient.status !== 'ready') {
                this.skip();
            }

            const config = { points: 10, duration: 60, softTimeoutMs: 1 }; // Very short timeout
            const keySelector = (req) => req.user.clientId;
            const middleware = RateLimiterService.createRateLimiter('test-timeout', config, keySelector);

            // Initialize the service first
            await RateLimiterService._initClient();

            // Mock a slow consume operation by stubbing the limiter
            const originalCreateRateLimiter = RateLimiterService._createRateLimiter;
            RateLimiterService._createRateLimiter = function(name, config) {
                const limiter = originalCreateRateLimiter.call(this, name, config);
                const originalConsume = limiter.consume;
                limiter.consume = function() {
                    return new Promise(resolve => setTimeout(() => resolve(originalConsume.apply(this, arguments)), 100));
                };
                return limiter;
            };

            await middleware(mockReq, mockRes, mockNext);

            // Should proceed due to timeout (fail-open)
            expect(mockNext.calledOnce).to.be.true;
            expect(mockLoggers.debug_log.warn.calledWith('RateLimiterService soft timeout')).to.be.true;

            // Restore original method
            RateLimiterService._createRateLimiter = originalCreateRateLimiter;
        });

        it('should handle general errors gracefully (fail-open)', async function () {
            if (!redisClient || redisClient.status !== 'ready') {
                this.skip();
            }

            const config = { points: 10, duration: 60 };
            const keySelector = (req) => req.user.clientId;
            const middleware = RateLimiterService.createRateLimiter('test-error', config, keySelector);

            // Initialize the service first
            await RateLimiterService._initClient();

            // Mock an error in the limiter
            const originalCreateRateLimiter = RateLimiterService._createRateLimiter;
            RateLimiterService._createRateLimiter = function(name, config) {
                return {
                    consume: () => Promise.reject(new Error('Redis error'))
                };
            };

            await middleware(mockReq, mockRes, mockNext);

            // Should proceed due to error (fail-open)
            expect(mockNext.calledOnce).to.be.true;
            expect(mockLoggers.errors_log.error.calledWith('RateLimiterService error (failing open):')).to.be.true;

            // Restore original method
            RateLimiterService._createRateLimiter = originalCreateRateLimiter;
        });
    });

    describe('Configuration Options', function () {
        let mockReq, mockRes, mockNext, createSpy;

        beforeEach(function () {
            mockReq = {
                method: 'GET',
                path: '/test',
                user: { clientId: 'config-test-client' }
            };
            mockRes = {
                set: sinon.stub(),
                status: sinon.stub().returnsThis(),
                json: sinon.stub()
            };
            mockNext = sinon.stub();
        });

        afterEach(function () {
            if (createSpy && createSpy.restore) {
                createSpy.restore();
            }
        });

        it('should use default blockDuration when not specified', async function () {
            if (!redisClient || redisClient.status !== 'ready') {
                this.skip();
            }

            const config = { points: 1, duration: 60 }; // No blockDuration specified
            const keySelector = (req) => req.user.clientId;

            // Test that the middleware works with default config
            // We can't easily spy on the internal RateLimiterRedis constructor,
            // but we can verify the middleware functions correctly
            const middleware = RateLimiterService.createRateLimiter('test-default-block', config, keySelector);

            // Ensure clean state
            RateLimiterService._client = null;
            RateLimiterService._initPromise = null;
            await RateLimiterService._initClient();

            // Trigger limiter creation and verify it works
            await middleware(mockReq, mockRes, mockNext);

            expect(mockNext.calledOnce).to.be.true;
            expect(mockRes.set.calledOnce).to.be.true;
        });

        it('should use custom blockDuration when specified', async function () {
            if (!redisClient || redisClient.status !== 'ready') {
                this.skip();
            }

            const config = { points: 1, duration: 60, blockDuration: 300 };
            const keySelector = (req) => req.user.clientId;

            createSpy = sinon.spy(RateLimiterService, '_createRateLimiter');

            const middleware = RateLimiterService.createRateLimiter('test-custom-block', config, keySelector);
            await RateLimiterService._initClient();

            await middleware(mockReq, mockRes, mockNext);

            expect(createSpy.calledOnce).to.be.true;
            const limiterConfig = createSpy.firstCall.args[1];
            expect(limiterConfig.blockDuration).to.equal(300);
        });

        it('should use default execEvenly when not specified', async function () {
            if (!redisClient || redisClient.status !== 'ready') {
                this.skip();
            }

            const config = { points: 1, duration: 60 };
            const keySelector = (req) => req.user.clientId;

            // Test that the middleware works with default execEvenly
            const middleware = RateLimiterService.createRateLimiter('test-default-exec', config, keySelector);

            // Ensure clean state
            RateLimiterService._client = null;
            RateLimiterService._initPromise = null;
            await RateLimiterService._initClient();

            await middleware(mockReq, mockRes, mockNext);

            expect(mockNext.calledOnce).to.be.true;
            expect(mockRes.set.calledOnce).to.be.true;
        });

        it('should use custom execEvenly when specified', async function () {
            if (!redisClient || redisClient.status !== 'ready') {
                this.skip();
            }

            const config = { points: 1, duration: 60, execEvenly: false };
            const keySelector = (req) => req.user.clientId;

            createSpy = sinon.spy(RateLimiterService, '_createRateLimiter');

            const middleware = RateLimiterService.createRateLimiter('test-custom-exec', config, keySelector);
            await RateLimiterService._initClient();

            await middleware(mockReq, mockRes, mockNext);

            expect(createSpy.calledOnce).to.be.true;
            const limiterConfig = createSpy.firstCall.args[1];
            expect(limiterConfig.execEvenly).to.be.false;
        });

        it('should use default soft timeout when not specified', async function () {
            const config = { points: 10, duration: 60 }; // No softTimeoutMs
            const keySelector = (req) => req.user.clientId;
            const middleware = RateLimiterService.createRateLimiter('test-default-timeout', config, keySelector);

            // The middleware should use DEFAULT_SOFT_TIMEOUT_MS (5000ms)
            // We can't easily test this without mocking internals, but we can verify it doesn't crash
            expect(typeof middleware).to.equal('function');
        });

        it('should use custom soft timeout when specified', async function () {
            const config = { points: 10, duration: 60, softTimeoutMs: 1000 };
            const keySelector = (req) => req.user.clientId;
            const middleware = RateLimiterService.createRateLimiter('test-custom-timeout', config, keySelector);

            expect(typeof middleware).to.equal('function');
        });
    });

    describe('Fail-Open Strategy', function () {
        let mockReq, mockRes, mockNext;

        beforeEach(function () {
            mockReq = {
                method: 'GET',
                path: '/test',
                user: { clientId: 'fail-open-client' }
            };
            mockRes = {
                set: sinon.stub(),
                status: sinon.stub().returnsThis(),
                json: sinon.stub()
            };
            mockNext = sinon.stub();
        });

        it('should proceed when Redis initialization fails', async function () {
            const config = { points: 10, duration: 60 };
            const keySelector = (req) => req.user.clientId;
            const middleware = RateLimiterService.createRateLimiter('test-init-fail', config, keySelector);

            // Mock initialization failure
            const originalInitClient = RateLimiterService._initClient;
            RateLimiterService._initClient = async () => false;

            await middleware(mockReq, mockRes, mockNext);

            expect(mockNext.calledOnce).to.be.true;
            expect(mockRes.status.called).to.be.false;

            // Restore
            RateLimiterService._initClient = originalInitClient;
        });

        it('should proceed when Redis client status is not ready', async function () {
            const config = { points: 10, duration: 60 };
            const keySelector = (req) => req.user.clientId;
            const middleware = RateLimiterService.createRateLimiter('test-not-ready', config, keySelector);

            // Mock client with non-ready status
            const mockClient = { status: 'connecting' };
            RateLimiterService._client = mockClient;

            await middleware(mockReq, mockRes, mockNext);

            expect(mockNext.calledOnce).to.be.true;
            expect(mockRes.status.called).to.be.false;
        });

        it('should log appropriate messages during fail-open scenarios', async function () {
            const config = { points: 10, duration: 60 };
            const keySelector = (req) => req.user.clientId;
            const middleware = RateLimiterService.createRateLimiter('test-logging', config, keySelector);

            // Test with null client
            RateLimiterService._client = null;
            RateLimiterService._initPromise = null;

            await middleware(mockReq, mockRes, mockNext);

            expect(mockNext.calledOnce).to.be.true;
            // No specific error should be logged for this case as it's expected behavior
        });
    });

    describe('Edge Cases', function () {
        let mockReq, mockRes, mockNext;

        beforeEach(function () {
            mockReq = {
                method: 'GET',
                path: '/test',
                user: { clientId: 'edge-case-client' }
            };
            mockRes = {
                set: sinon.stub(),
                status: sinon.stub().returnsThis(),
                json: sinon.stub()
            };
            mockNext = sinon.stub();
        });

        it('should handle async key selector', async function () {
            const config = { points: 10, duration: 60 };
            const keySelector = async (req) => {
                // Simulate async operation
                await new Promise(resolve => setTimeout(resolve, 10));
                return req.user.clientId;
            };
            const middleware = RateLimiterService.createRateLimiter('test-async-key', config, keySelector);

            await middleware(mockReq, mockRes, mockNext);

            expect(mockNext.calledOnce).to.be.true;
        });

        it('should handle key selector that returns undefined', async function () {
            const config = { points: 10, duration: 60 };
            const keySelector = () => undefined;
            const middleware = RateLimiterService.createRateLimiter('test-undefined-key', config, keySelector);

            await middleware(mockReq, mockRes, mockNext);

            expect(mockNext.calledOnce).to.be.true;
            expect(mockRes.set.called).to.be.false;
        });

        it('should handle key selector that returns empty string', async function () {
            const config = { points: 10, duration: 60 };
            const keySelector = () => '';
            const middleware = RateLimiterService.createRateLimiter('test-empty-key', config, keySelector);

            await middleware(mockReq, mockRes, mockNext);

            expect(mockNext.calledOnce).to.be.true;
            expect(mockRes.set.called).to.be.false;
        });

        it('should create limiter only once per middleware instance', async function () {
            if (!redisClient || redisClient.status !== 'ready') {
                this.skip();
            }

            const config = { points: 10, duration: 60 };
            const keySelector = (req) => req.user.clientId;

            await RateLimiterService._initClient();

            // Create middleware and call it once to initialize the limiter
            const middleware = RateLimiterService.createRateLimiter('test-single-limiter', config, keySelector);
            await middleware(mockReq, mockRes, mockNext);

            // Now spy on _createRateLimiter to see if it's called again
            const createSpy = sinon.spy(RateLimiterService, '_createRateLimiter');

            // Reset mocks and call middleware again
            mockNext.resetHistory();
            mockRes.set.resetHistory();
            await middleware(mockReq, mockRes, mockNext);

            // _createRateLimiter should not be called again since limiter is cached
            expect(createSpy.called).to.be.false;

            createSpy.restore();
        });
    });
});
