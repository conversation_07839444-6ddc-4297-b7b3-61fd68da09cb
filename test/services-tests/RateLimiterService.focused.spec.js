'use strict';

const sinon = require('sinon');
const chai = require('chai');
const chaiAsPromised = require('chai-as-promised');

chai.use(chaiAsPromised);
const expect = chai.expect;

// Minimal globals for isolated testing (created only if not present)
const mockSails = {
    config: { connections: { redis: process.env.REDIS_URL } },
    once: sinon.stub(),
    services: {}
};

const mockLoggers = {
    debug_log: { verbose: sinon.stub(), warn: sinon.stub() },
    errors_log: { error: sinon.stub() }
};

if (!global.sails) global.sails = mockSails;
if (!global.loggers) global.loggers = mockLoggers;

const RateLimiterService = require('../../api/services/RateLimiterService');
global.RateLimiterService = RateLimiterService;

describe('RateLimiterService - Unit Tests (deterministic, no Redis)', function () {
    let sandbox;

    function makeReqResNext(overrides) {
        overrides = overrides || {};
        const req = Object.assign(
            {
                method: 'GET',
                path: '/test',
                user: { clientId: 'test-client' }
            },
            overrides.req || {}
        );

        const res = Object.assign(
            {
                set: sinon.stub(),
                status: sinon.stub().returnsThis(),
                json: sinon.stub()
            },
            overrides.res || {}
        );

        const next = sinon.stub();
        return { req, res, next };
    }

    beforeEach(function () {
        sandbox = sinon.createSandbox();

        // Reset global stubs if present
        for (const k in global.loggers.debug_log) {
            if (global.loggers.debug_log[k] && global.loggers.debug_log[k].resetHistory) {
                global.loggers.debug_log[k].resetHistory();
            }
        }
        for (const k in global.loggers.errors_log) {
            if (global.loggers.errors_log[k] && global.loggers.errors_log[k].resetHistory) {
                global.loggers.errors_log[k].resetHistory();
            }
        }
        if (global.sails === mockSails && mockSails.once.resetHistory) {
            mockSails.once.resetHistory();
        }

        // Reset internal state between tests
        RateLimiterService._client = null;
        RateLimiterService._initPromise = null;
    });

    afterEach(function () {
        sandbox.restore();
        RateLimiterService._client = null;
        RateLimiterService._initPromise = null;
    });

    describe('createRateLimiter basics', function () {
        it('returns a middleware function', function () {
            const mw = RateLimiterService.createRateLimiter(
                'ns',
                { points: 10, duration: 60 },
                function (req) {
                    return req && req.user ? req.user.clientId : undefined;
                }
            );
            expect(typeof mw).to.equal('function');
        });

        it('skips OPTIONS requests', async function () {
            const ctx = makeReqResNext({ req: { method: 'OPTIONS' } });
            const mw = RateLimiterService.createRateLimiter(
                'ns',
                { points: 10, duration: 60 },
                function (r) {
                    return r && r.user ? r.user.clientId : undefined;
                }
            );
            await mw(ctx.req, ctx.res, ctx.next);
            expect(ctx.next.calledOnce).to.be.true;
            expect(ctx.res.set.called).to.be.false;
            expect(ctx.res.status.called).to.be.false;
        });

        it('skips when key selector returns null/undefined/empty', async function () {
            const configs = [
                { ks: function () { return null; } },
                { ks: function () { return undefined; } },
                { ks: function () { return ''; } }
            ];

            for (let i = 0; i < configs.length; i++) {
                const ctx = makeReqResNext();
                const mw = RateLimiterService.createRateLimiter('ns', { points: 10, duration: 60 }, configs[i].ks);
                await mw(ctx.req, ctx.res, ctx.next);
                expect(ctx.next.calledOnce).to.be.true;
                expect(ctx.res.set.called).to.be.false;
            }
        });

        it('handles async key selector', async function () {
            const ctx = makeReqResNext();
            const keySelector = async function (r) {
                await new Promise(function (resolve) { setTimeout(resolve, 5); });
                return r && r.user ? r.user.clientId : undefined;
            };
            // Ensure middleware proceeds regardless of client state (fail-open)
            sandbox.stub(RateLimiterService, '_initClient').resolves(false);

            const mw = RateLimiterService.createRateLimiter('ns', { points: 10, duration: 60 }, keySelector);
            await mw(ctx.req, ctx.res, ctx.next);

            expect(ctx.next.calledOnce).to.be.true;
            expect(ctx.res.set.called).to.be.false;
        });

        it('logs and proceeds when key selector throws', async function () {
            const ctx = makeReqResNext();
            // Force service to initialize so we definitely execute selector path
            sandbox.stub(RateLimiterService, '_initClient').callsFake(async function () {
                RateLimiterService._client = { status: 'ready' };
                return true;
            });

            const keySelector = function () {
                throw new Error('Key selector error');
            };

            const mw = RateLimiterService.createRateLimiter('ns', { points: 10, duration: 60 }, keySelector);
            await mw(ctx.req, ctx.res, ctx.next);

            expect(ctx.next.calledOnce).to.be.true;
            expect(ctx.res.set.called).to.be.false;
            expect(global.loggers.errors_log.error.called).to.be.true;
            expect(String(global.loggers.errors_log.error.firstCall.args[0] || '')).to.include('RateLimiterService key selector error');
        });

        it('proceeds (fail-open) when initialization fails', async function () {
            const ctx = makeReqResNext();
            sandbox.stub(RateLimiterService, '_initClient').resolves(false);

            const mw = RateLimiterService.createRateLimiter(
                'ns',
                { points: 10, duration: 60 },
                function (r) { return r && r.user ? r.user.clientId : undefined; }
            );
            await mw(ctx.req, ctx.res, ctx.next);

            expect(ctx.next.calledOnce).to.be.true;
            expect(ctx.res.set.called).to.be.false;
            expect(ctx.res.status.called).to.be.false;
        });

        it('proceeds (fail-open) when client is not ready', async function () {
            const ctx = makeReqResNext();

            sandbox.stub(RateLimiterService, '_initClient').callsFake(async function () {
                RateLimiterService._client = { status: 'connecting' };
                return true;
            });

            const mw = RateLimiterService.createRateLimiter(
                'ns',
                { points: 10, duration: 60 },
                function (r) { return r && r.user ? r.user.clientId : undefined; }
            );
            await mw(ctx.req, ctx.res, ctx.next);

            expect(ctx.next.calledOnce).to.be.true;
            expect(ctx.res.set.called).to.be.false;
            expect(ctx.res.status.called).to.be.false;
        });
    });

    describe('Utility methods', function () {
        it('soft timeout: resolves fast promise', async function () {
            const result = await RateLimiterService._softTimeout(Promise.resolve('fast'), 1000);
            expect(result).to.equal('fast');
        });

        it('soft timeout: times out slow promise', async function () {
            const clock = sandbox.useFakeTimers();
            const slow = new Promise(function (resolve) {
                setTimeout(function () { resolve('slow'); }, 100);
            });
            const retP = RateLimiterService._softTimeout(slow, 50);

            // Old Sinon fake timers: advance using tick (not tickAsync)
            clock.tick(60);
            const result = await retP;
            expect(result).to.equal('__RL_TIMEOUT__');

            // Let the slow promise settle to avoid dangling timers
            clock.tick(100);
        });

        it('getKeySelectorSafe: returns selected key', async function () {
            const req = { user: { id: 'u-1' } };
            const keySelector = async function (r) { return r.user.id; };
            const result = await RateLimiterService._getKeySelectorSafe(keySelector, req);
            expect(result).to.equal('u-1');
        });

        it('getKeySelectorSafe: logs and returns null on error', async function () {
            if (global.loggers.errors_log.error.resetHistory) global.loggers.errors_log.error.resetHistory();
            const req = {};
            const keySelector = async function () { throw new Error('boom'); };
            const result = await RateLimiterService._getKeySelectorSafe(keySelector, req);
            expect(result).to.equal(null);
            expect(global.loggers.errors_log.error.called).to.be.true;
            expect(String(global.loggers.errors_log.error.firstCall.args[0] || '')).to.include('RateLimiterService key selector error');
        });
    });

    describe('Generic policy patterns', function () {
        it('client-based key selector pattern proceeds', async function () {
            const ctx = makeReqResNext({
                req: {
                    method: 'GET',
                    path: '/api/service/v1/data',
                    user: { clientId: 'api-client-123' }
                }
            });

            sandbox.stub(RateLimiterService, '_initClient').resolves(false);

            const keySelector = function (r) { return r && r.user ? r.user.clientId : undefined; };
            const mw = RateLimiterService.createRateLimiter('client-pattern', { points: 10, duration: 60 }, keySelector);
            await mw(ctx.req, ctx.res, ctx.next);

            expect(ctx.next.calledOnce).to.be.true;
            expect(ctx.res.set.called).to.be.false;
        });

        it('accepts custom soft timeout configuration', async function () {
            const mw = RateLimiterService.createRateLimiter(
                'custom-timeout',
                { points: 10, duration: 60, softTimeoutMs: 750 },
                function (r) { return r && r.user ? r.user.clientId : undefined; }
            );
            expect(typeof mw).to.equal('function');
        });
    });
});
