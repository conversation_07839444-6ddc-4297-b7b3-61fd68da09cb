'use strict';

function clearDB () {
	return Promise.all([
		Db.query('DELETE FROM "banned_email"'),
		Db.query('DELETE FROM "banned_fingerprint"')
	]);
}

describe('banService', function () {
	
	let service;

	let checkRowObject = (row, tableName) => {
		expect(row).to.be.a('object');

		let fields = [
			'created',
			'modified',
			'reason',
			'history',
			'unlocked'
		];

		if (tableName === 'banned_email') {
			fields.push('banned_email_id', 'email');
		} else {
			fields.push('banned_fingerprint_id', 'fingerprint');
		}

		expect(row).to.contain.all.keys(fields);

		expect(row.unlocked).to.be.false;

		expect(row.history).to.be.an('array');

		expect(row.history.length).to.equal(1);

		expect(row.history[0]).to.contain.all.keys([
			'type', 
			'attempt', 
			'event'
		])

		expect(row.history[0].event).to.equal(1);

		expect(row.history[0].type).to.equal('dispute on existing teams payment');
	}

	before(() => {
		service = sails.services.banservice;
	})

	after(() => clearDB())

	context('banEmail()', function () {
		
		it('should create "banned_email" row', () => {
			let email 			= '<EMAIL>', 
				reason 			= 'dispute',
				eventID 		= 1,
                purchaseID      = 2,
                paymentFor 		= 'teams';

			return service.banEmail(email, reason, eventID, purchaseID, paymentFor)
			.then(row => checkRowObject(row, 'banned_email'))
		})

	});

	context('banFingerprint()', function () {
		
		it('should create "banned_fingerprint" row', () => {
			let fingerprint 	= 'fingerprint', 
				reason 			= 'dispute',
				eventID 		= 1,
                purchaseID      = 2,
				paymentFor 		= 'teams';

			return service.banFingerprint(fingerprint, reason, eventID, purchaseID, paymentFor)
			.then(row => checkRowObject(row, 'banned_fingerprint'))
		})

	})


	context('banLostDisputes()', function () {

		let disputesListStub, 
			getPurchaseEmailStub,

			banFingerprintSpy,
			banEmailSpy;

		before(() => {
			// https://nodejs.org/api/globals.html#globals_require
			let StripeConnectModule = require.cache[
				require.resolve('../../api/lib/StripeConnect')
			].exports;

			disputesListStub = sinon.stub(StripeConnectModule, 'getLostDisputes').callsFake(function () {
				return Promise.resolve([
					{ charge_id:  1, fingerprint: 'fp1' , email: '<EMAIL>' },
					{ charge_id:  2, fingerprint: 'fp2' , email: '<EMAIL>' },
					{ charge_id:  3, fingerprint: 'fp3' , email: '<EMAIL>' }
				]);
			});

			getPurchaseEmailStub = sinon.stub(service, 'getPurchaseEmail').callsFake(function (chargeID) {
				return new Promise(resolve => {
					if (chargeID === 1) {
						resolve({ event_id: 1, email: '<EMAIL>', payment_for: 'teams' });
					} else if (chargeID === 2) {
						resolve({ event_id: 2, email: '<EMAIL>', payment_for: 'tickets' });
					} else  if (chargeID === 3) {
						resolve({ event_id: 3, email: '<EMAIL>', payment_for: 'booths' });
					}
				})
			});

			banFingerprintSpy = sinon.spy(service, 'banFingerprint');

			banEmailSpy = sinon.spy(service, 'banEmail');
		})

		after(() => {
			disputesListStub.restore();
			getPurchaseEmailStub.restore();

			banFingerprintSpy.restore();
			banEmailSpy.restore();
		})

		it('should process lost disputes', () => {
			return service.banLostDisputes()
			.then(() => {
				expect(disputesListStub.calledOnce).to.be.true;
				/* One call for every dispute object */
				expect(getPurchaseEmailStub.callCount).to.equal(3);

				/* One call for every fingerprint in dispute object (3 objects = 3 fingerprints) */
				expect(banFingerprintSpy.callCount).to.equal(3);
				/* 
				* One call per unique email: emails from purchase row in test data differs from 
				* email in disputes, so for every of 3 disputes we have 2 unique emails
				* */
				expect(banEmailSpy.callCount).to.equal(6);
			})
		})

	})

})
